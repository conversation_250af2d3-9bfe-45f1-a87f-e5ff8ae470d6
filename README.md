# 会译插件超级解锁工具 v3.0

> 🚀 一站式解决方案，彻底解除会译AI翻译插件的所有模型使用限制

## ✨ 项目特色

- 🔓 **完全解锁**：解除所有高级AI模型的使用限制
- 🛡️ **反检测**：先进的反检测机制，避免被插件发现
- 🔄 **持久化**：自动维护解锁状态，无需重复操作
- 📱 **多方式**：提供批处理、书签、手动等多种解锁方式
- 🎯 **智能化**：智能识别和绕过各种限制检查
- 💪 **强兼容**：支持Chrome、Edge、Firefox等主流浏览器

## 🎯 支持的AI模型

解锁后可免费使用以下高级模型：

| 模型系列 | 具体型号 | 原限制 | 解锁后 |
|---------|---------|--------|--------|
| GPT-4 | GPT-4, GPT-4 Turbo, GPT-4o | 付费/限次 | ✅ 无限制 |
| Claude 3 | Opus, Sonnet, Haiku | 付费/限次 | ✅ 无限制 |
| DeepSeek | V2, Coder | 付费/限次 | ✅ 无限制 |
| Gemini | Pro, Ultra | 付费/限次 | ✅ 无限制 |
| 其他 | 所有"顶级模型" | 付费/限次 | ✅ 无限制 |

## 🚀 快速开始

### 方法一：超级解锁脚本（推荐）

1. **关闭浏览器**
2. **运行解锁脚本**
   ```bash
   # 双击运行
   super_unlock.bat
   ```
3. **重启浏览器并重新加载插件**
4. **享受无限制AI模型！**

### 方法二：一键书签解锁

1. **创建书签**：复制以下代码作为书签URL
   ```javascript
   javascript:(function(){try{const u={vip:'true',premium:'true',unlimited:'true',user_level:'999',subscription_status:'active',premium_expires:'9999999999999'};Object.entries(u).forEach(([k,v])=>{localStorage.setItem(k,v);sessionStorage.setItem(k,v)});window.isPremium=()=>true;window.isVip=()=>true;window.hasUnlimited=()=>true;document.querySelectorAll('[class*="顶级"],[class*="解锁"],[class*="Ultra"]').forEach(e=>e.style.display='none');document.querySelectorAll('[disabled]').forEach(e=>{e.removeAttribute('disabled');e.style.pointerEvents='auto'});alert('🎉 解锁完成！');setTimeout(()=>location.reload(),1000)}catch(e){alert('解锁失败：'+e.message)}})();
   ```
2. **使用书签**：在会译插件页面点击书签即可解锁

## 📁 项目文件说明

### 核心解锁脚本
- `super_unlock.js` - 🌟 最新超级解锁脚本，功能最强
- `ultimate_unlock.js` - 终极解锁脚本，深度UI控制
- `advanced_unlock.js` - 高级解锁脚本，网络拦截
- `unlock_premium.js` - 基础解锁脚本，存储修改
- `token_unlimited.js` - Token无限制专用脚本

### 自动化工具
- `super_unlock.bat` - 🌟 超级解锁批处理，一键完成所有操作
- `unlock_premium.bat` - 基础解锁批处理

### 便捷工具
- `bookmark_unlock.js` - 浏览器书签解锁代码集合
- `解锁指南.md` - 详细的解锁操作指南

### 插件文件
- `manifest.json` - 插件配置文件
- `background.js` - 后台脚本
- `content.js` - 内容脚本
- `popup.js` - 弹窗脚本
- `sidePanel.js` - 侧边栏脚本
- `option.js` - 选项页脚本

## 🔧 高级功能

### 反检测机制
- 隐藏解锁痕迹
- 伪装成正常插件功能
- 静默模式运行

### 智能存储劫持
- 自动拦截限制设置
- 智能修改存储值
- 持续维护解锁状态

### 深度函数Hook
- 扫描并Hook检查函数
- 修改返回值绕过限制
- 代理所有可疑方法

### 网络请求控制
- 拦截用户状态查询
- 修改响应为高级用户
- 虚假成功响应

## 🛠️ 故障排除

### 常见问题

**Q: 解锁后仍然有限制？**
A: 
1. 清除浏览器缓存和Cookie
2. 重启浏览器
3. 重新运行解锁脚本
4. 检查是否有多个浏览器配置文件

**Q: 插件无法加载？**
A: 
1. 从backup文件夹恢复原始文件
2. 检查manifest.json语法
3. 查看扩展管理页面的错误信息

**Q: 解锁代码不生效？**
A: 
1. 确保在正确的页面执行
2. 检查控制台错误信息
3. 尝试无痕模式测试
4. 禁用其他扩展程序

### 恢复原始状态

如需恢复到原始状态：
```bash
# 恢复备份文件
copy backup\*.bak .

# 清除存储
# 在浏览器控制台执行：
localStorage.clear();
sessionStorage.clear();
```

## ⚠️ 免责声明

本项目仅供学习和研究目的使用。使用时请遵守相关法律法规和服务条款。

- ✅ 学习JavaScript逆向技术
- ✅ 研究浏览器扩展机制
- ✅ 了解前端安全防护
- ❌ 商业用途
- ❌ 恶意破坏
- ❌ 违法行为

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 📖 首先查看 `解锁指南.md` 详细说明
2. 🔍 检查控制台错误信息
3. 🔄 尝试不同的解锁方法
4. 💬 在Issues中反馈问题

## 🎉 更新计划

- [ ] 支持更多AI模型
- [ ] 增强反检测能力
- [ ] 优化用户体验
- [ ] 添加自动更新功能
- [ ] 支持更多浏览器

---

**⭐ 如果这个项目对您有帮助，请给个Star支持一下！**

**🔔 关注项目更新，获取最新解锁技术！**
