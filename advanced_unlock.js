// 会译插件高级解锁脚本 - 专门处理混淆代码
// 使用多种方法绕过可能的模型限制检查

(function() {
    'use strict';
    
    console.log('=== 会译插件高级解锁脚本启动 ===');
    
    // 1. 拦截和重写常见的限制检查函数
    function interceptCommonChecks() {
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest;
        
        // 拦截fetch请求
        window.fetch = function(...args) {
            console.log('拦截fetch请求:', args[0]);
            return originalFetch.apply(this, args).then(response => {
                const url = args[0];
                if (typeof url === 'string' && 
                    (url.includes('user') || url.includes('subscription') || 
                     url.includes('membership') || url.includes('limit'))) {
                    
                    console.log('检测到用户状态查询，尝试修改响应');
                    return response.clone().text().then(text => {
                        try {
                            const data = JSON.parse(text);
                            // 修改响应数据为高级用户
                            data.premium = true;
                            data.vip = true;
                            data.unlimited = true;
                            data.level = 999;
                            data.subscription = 'active';
                            
                            return new Response(JSON.stringify(data), {
                                status: response.status,
                                statusText: response.statusText,
                                headers: response.headers
                            });
                        } catch (e) {
                            return response;
                        }
                    });
                }
                return response;
            });
        };
        
        // 拦截XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._url = url;
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            const originalOnLoad = xhr.onload;
            const originalOnReadyStateChange = xhr.onreadystatechange;
            
            xhr.onload = function() {
                if (xhr._url && (xhr._url.includes('user') || xhr._url.includes('subscription'))) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        response.premium = true;
                        response.vip = true;
                        response.unlimited = true;
                        
                        Object.defineProperty(xhr, 'responseText', {
                            value: JSON.stringify(response),
                            writable: false
                        });
                    } catch (e) {}
                }
                if (originalOnLoad) originalOnLoad.apply(this, arguments);
            };
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr._url && 
                    (xhr._url.includes('user') || xhr._url.includes('subscription'))) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        response.premium = true;
                        response.vip = true;
                        response.unlimited = true;
                        
                        Object.defineProperty(xhr, 'responseText', {
                            value: JSON.stringify(response),
                            writable: false
                        });
                    } catch (e) {}
                }
                if (originalOnReadyStateChange) originalOnReadyStateChange.apply(this, arguments);
            };
            
            return originalSend.apply(this, arguments);
        };
        
        console.log('网络请求拦截设置完成');
    }
    
    // 2. 重写可能的混淆函数
    function rewriteObfuscatedFunctions() {
        // 尝试找到并重写可能的检查函数
        const possibleCheckFunctions = [];
        
        // 扫描全局对象中的函数
        function scanGlobalFunctions(obj, path = '') {
            for (let key in obj) {
                try {
                    const value = obj[key];
                    const fullPath = path ? `${path}.${key}` : key;
                    
                    if (typeof value === 'function') {
                        const funcStr = value.toString();
                        // 检查函数是否可能与限制检查相关
                        if (funcStr.includes('premium') || funcStr.includes('vip') || 
                            funcStr.includes('limit') || funcStr.includes('subscription') ||
                            funcStr.includes('level') || funcStr.includes('unlock')) {
                            possibleCheckFunctions.push({path: fullPath, func: value});
                        }
                    } else if (typeof value === 'object' && value !== null && 
                              path.split('.').length < 3) { // 限制递归深度
                        scanGlobalFunctions(value, fullPath);
                    }
                } catch (e) {
                    // 忽略无法访问的属性
                }
            }
        }
        
        scanGlobalFunctions(window);
        console.log('找到可能的检查函数:', possibleCheckFunctions.length);
        
        // 尝试重写这些函数
        possibleCheckFunctions.forEach(({path, func}) => {
            try {
                const pathParts = path.split('.');
                let obj = window;
                for (let i = 0; i < pathParts.length - 1; i++) {
                    obj = obj[pathParts[i]];
                }
                
                const funcName = pathParts[pathParts.length - 1];
                const originalFunc = obj[funcName];
                
                obj[funcName] = function(...args) {
                    console.log(`拦截到可能的检查函数调用: ${path}`);
                    
                    // 尝试调用原函数
                    try {
                        const result = originalFunc.apply(this, args);
                        
                        // 如果返回值看起来像限制检查结果，修改它
                        if (typeof result === 'boolean') {
                            return true; // 假设true表示有权限
                        } else if (typeof result === 'object' && result !== null) {
                            if ('premium' in result) result.premium = true;
                            if ('vip' in result) result.vip = true;
                            if ('unlimited' in result) result.unlimited = true;
                            if ('level' in result) result.level = 999;
                            if ('limit' in result) result.limit = 999999;
                        }
                        
                        return result;
                    } catch (e) {
                        // 如果原函数出错，返回表示有权限的值
                        return true;
                    }
                };
                
                console.log(`已重写函数: ${path}`);
            } catch (e) {
                console.log(`重写函数失败: ${path}`, e);
            }
        });
    }
    
    // 3. 持续监控和修改存储
    function continuousStorageMonitoring() {
        const premiumData = {
            vip: true,
            premium: true,
            pro: true,
            unlimited: true,
            subscription: 'active',
            level: 999,
            tier: 'premium',
            plan: 'unlimited',
            expires: 9999999999999,
            limits: false,
            restricted: false
        };
        
        // 监控localStorage变化
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            console.log(`LocalStorage设置: ${key} = ${value}`);
            
            // 检查是否是限制相关的键
            const restrictionKeys = ['limit', 'restrict', 'premium', 'vip', 'subscription'];
            if (restrictionKeys.some(k => key.toLowerCase().includes(k))) {
                console.log(`检测到限制相关键，修改为解锁状态: ${key}`);
                if (key.toLowerCase().includes('limit') || key.toLowerCase().includes('restrict')) {
                    value = 'false';
                } else {
                    value = 'true';
                }
            }
            
            return originalSetItem.call(this, key, value);
        };
        
        // 定期设置高级用户状态
        setInterval(() => {
            Object.keys(premiumData).forEach(key => {
                localStorage.setItem(key, premiumData[key]);
                sessionStorage.setItem(key, premiumData[key]);
            });
            
            // 如果有chrome.storage，也设置
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.set(premiumData);
                chrome.storage.sync.set(premiumData);
            }
        }, 2000);
        
        console.log('存储监控和修改已启动');
    }
    
    // 4. 代理所有可能的检查方法
    function proxyAllChecks() {
        // 创建一个代理处理器，拦截所有属性访问
        const handler = {
            get: function(target, prop, receiver) {
                const value = Reflect.get(target, prop, receiver);
                
                // 如果是函数，且名称包含限制相关词汇，进行拦截
                if (typeof value === 'function') {
                    const propStr = prop.toString().toLowerCase();
                    const restrictionWords = ['check', 'verify', 'validate', 'limit', 'restrict', 'premium', 'vip'];
                    
                    if (restrictionWords.some(word => propStr.includes(word))) {
                        return function(...args) {
                            console.log(`拦截到可疑检查方法: ${prop}`);
                            try {
                                const result = value.apply(this, args);
                                
                                // 修改返回值以绕过限制
                                if (typeof result === 'boolean') {
                                    return true;
                                } else if (typeof result === 'object' && result !== null) {
                                    return { ...result, premium: true, vip: true, unlimited: true };
                                }
                                
                                return result;
                            } catch (e) {
                                return true; // 如果出错，假设有权限
                            }
                        };
                    }
                }
                
                return value;
            }
        };
        
        // 代理常见的可能包含检查方法的对象
        if (window.huiyi) window.huiyi = new Proxy(window.huiyi, handler);
        if (window.app) window.app = new Proxy(window.app, handler);
        if (window.config) window.config = new Proxy(window.config, handler);
        
        console.log('方法代理设置完成');
    }
    
    // 5. 注入CSS隐藏可能的限制提示
    function hideLimitationUI() {
        const style = document.createElement('style');
        style.textContent = `
            /* 隐藏可能的限制提示 */
            [class*="limit"], [class*="restrict"], [class*="premium"], 
            [class*="upgrade"], [class*="vip"], [id*="limit"], 
            [id*="restrict"], [id*="premium"], [id*="upgrade"], [id*="vip"] {
                display: none !important;
            }
            
            /* 显示所有功能按钮 */
            [class*="unlock"], [class*="pro"], [class*="advanced"] {
                display: block !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }
        `;
        
        if (document.head) {
            document.head.appendChild(style);
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                document.head.appendChild(style);
            });
        }
        
        console.log('UI限制隐藏样式已注入');
    }
    
    // 6. 主执行函数
    function executeUnlock() {
        console.log('开始执行解锁操作...');
        
        interceptCommonChecks();
        rewriteObfuscatedFunctions();
        continuousStorageMonitoring();
        proxyAllChecks();
        hideLimitationUI();
        
        // 等待页面加载完成后再次执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    rewriteObfuscatedFunctions();
                    proxyAllChecks();
                }, 1000);
            });
        }
        
        console.log('=== 解锁操作完成 ===');
        console.log('如果仍然有限制，请刷新页面或重启浏览器');
    }
    
    // 立即执行
    executeUnlock();
    
    // 延迟再次执行，确保捕获到延迟加载的代码
    setTimeout(executeUnlock, 3000);
    setTimeout(executeUnlock, 10000);
    
})();

// 导出到全局作用域，便于手动调用
window.huiyiUnlock = function() {
    console.log('手动触发解锁');
    location.reload();
};