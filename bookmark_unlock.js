// 会译插件书签解锁脚本 - 可保存为浏览器书签一键执行
// 使用方法：复制下面的javascript代码，保存为浏览器书签，在会译插件页面点击书签即可解锁

// 完全解锁版本（登录绕过+模型解锁）：
javascript:(function(){try{const d={logged_in:'true',is_logged_in:'true',authenticated:'true',user_id:'premium_user_12345',vip:'true',premium:'true',unlimited:'true',user_level:'999',subscription_status:'active',premium_expires:'*************',token_limit:'999999'};Object.entries(d).forEach(([k,v])=>{localStorage.setItem(k,v);sessionStorage.setItem(k,v)});window.isLoggedIn=()=>true;window.checkLogin=()=>true;window.isPremium=()=>true;window.isVip=()=>true;window.hasUnlimited=()=>true;window.getUserInfo=()=>d;document.querySelectorAll('[class*="login"],[class*="登录"],[class*="顶级"],[class*="解锁"],[class*="Ultra"]').forEach(e=>e.style.display='none');document.querySelectorAll('[disabled]').forEach(e=>{e.removeAttribute('disabled');e.style.pointerEvents='auto'});if(typeof chrome!=='undefined'&&chrome.storage){const s={};Object.entries(d).forEach(([k,v])=>{s[k]=v==='true'?true:(isNaN(v)?v:parseInt(v))});chrome.storage.local.set(s);chrome.storage.sync.set(s)}alert('🎉 完全解锁完成！\n✅ 已绕过登录限制\n✅ 已解锁所有模型\n页面将刷新...');setTimeout(()=>location.reload(),1500)}catch(e){alert('解锁失败：'+e.message)}})();

// 标准书签版本（仅模型解锁）：
javascript:(function(){try{const u={vip:'true',premium:'true',unlimited:'true',user_level:'999',subscription_status:'active',premium_expires:'*************',token_limit:'999999'};Object.entries(u).forEach(([k,v])=>{localStorage.setItem(k,v);sessionStorage.setItem(k,v)});window.isPremium=()=>true;window.isVip=()=>true;window.hasUnlimited=()=>true;document.querySelectorAll('[class*="顶级"],[class*="解锁"],[class*="Ultra"],[class*="limit"]').forEach(e=>e.style.display='none');document.querySelectorAll('[disabled],[class*="disabled"]').forEach(e=>{e.removeAttribute('disabled');e.style.pointerEvents='auto';e.style.opacity='1'});if(typeof chrome!=='undefined'&&chrome.storage){chrome.storage.local.set({vip:true,premium:true,unlimited:true,user_level:999});chrome.storage.sync.set({vip:true,premium:true,unlimited:true,user_level:999})}alert('🎉 会译插件解锁完成！刷新页面生效');setTimeout(()=>location.reload(),1000)}catch(e){alert('解锁失败：'+e.message)}})();

// 登录绕过专用版本：
javascript:(function(){try{const l={logged_in:'true',is_logged_in:'true',authenticated:'true',auth_status:'authenticated',login_status:'active',session_valid:'true',user_id:'premium_user_12345',username:'premium_user',email:'<EMAIL>'};Object.entries(l).forEach(([k,v])=>{localStorage.setItem(k,v);sessionStorage.setItem(k,v)});window.isLoggedIn=()=>true;window.checkLogin=()=>true;window.getUserStatus=()=>'authenticated';window.getUserInfo=()=>l;window.currentUser=l;document.querySelectorAll('[class*="login"],[class*="登录"],[class*="sign-in"],[class*="auth"]').forEach(e=>e.style.display='none');const s=document.createElement('style');s.textContent='[class*="login"],[class*="登录"],[class*="mask"],[class*="overlay"]{display:none!important}[class*="content"],[class*="main"]{display:block!important;opacity:1!important}';document.head.appendChild(s);if(typeof chrome!=='undefined'&&chrome.storage){const cs={};Object.entries(l).forEach(([k,v])=>{cs[k]=v==='true'?true:v});chrome.storage.local.set(cs)}alert('🔐 登录绕过完成！\n现在可以无需登录使用插件');setTimeout(()=>location.reload(),1000)}catch(e){alert('登录绕过失败：'+e.message)}})();

// 增强书签版本（功能更全面）：
javascript:(function(){try{console.log('🚀 会译插件书签解锁启动');const unlockData={vip:'true',premium:'true',pro:'true',unlimited:'true',user_level:'999',subscription_status:'active',premium_expires:'*************',token_limit:'999999',daily_limit:'999999',remaining_tokens:'999999',account_type:'premium',plan:'unlimited',tier:'premium'};Object.entries(unlockData).forEach(([key,value])=>{localStorage.setItem(key,value);sessionStorage.setItem(key,value);window[key]=value==='true'?true:(isNaN(value)?value:parseInt(value))});['isPremium','isVip','isPro','hasUnlimited','checkPremium','checkVip','getUserLevel'].forEach(fn=>{window[fn]=()=>true});window.getUserLevel=()=>999;window.getSubscriptionStatus=()=>'active';const hideSelectors=['[class*="顶级"]','[class*="解锁"]','[class*="Ultra"]','[class*="premium"]','[class*="upgrade"]','[class*="limit"]','[class*="restrict"]','[id*="顶级"]','[id*="解锁"]','[id*="Ultra"]'];hideSelectors.forEach(selector=>{document.querySelectorAll(selector).forEach(el=>el.style.display='none')});const enableSelectors=['[disabled]','[class*="disabled"]','[class*="locked"]'];enableSelectors.forEach(selector=>{document.querySelectorAll(selector).forEach(el=>{el.removeAttribute('disabled');el.style.pointerEvents='auto';el.style.opacity='1';el.style.cursor='pointer'})});const style=document.createElement('style');style.textContent=`[class*="model"],[class*="option"]{display:block!important;opacity:1!important;pointer-events:auto!important}[class*="limit"],[class*="restrict"]{display:none!important}`;document.head.appendChild(style);if(typeof chrome!=='undefined'&&chrome.storage){const storageData={};Object.entries(unlockData).forEach(([key,value])=>{storageData[key]=value==='true'?true:(isNaN(value)?value:parseInt(value))});chrome.storage.local.set(storageData);chrome.storage.sync.set(storageData)}const originalSetItem=localStorage.setItem;localStorage.setItem=function(key,value){if(key.includes('premium')||key.includes('vip')||key.includes('unlimited'))value='true';if(key.includes('level'))value='999';if(key.includes('limit')&&!key.includes('unlimited'))value='999999';return originalSetItem.call(this,key,value)};setInterval(()=>{Object.entries(unlockData).forEach(([key,value])=>{localStorage.setItem(key,value)})},3000);console.log('✅ 书签解锁完成');alert('🎉 会译插件解锁完成！\n\n✅ 已设置高级用户状态\n✅ 已隐藏限制提示\n✅ 已启用所有功能\n\n页面将在2秒后刷新...');setTimeout(()=>location.reload(),2000)}catch(e){console.error('解锁失败:',e);alert('❌ 解锁失败：'+e.message+'\n\n请在会译插件页面重试')}})();

// 静默书签版本（无弹窗提示）：
javascript:(function(){try{const u={vip:'true',premium:'true',unlimited:'true',user_level:'999',subscription_status:'active',premium_expires:'*************'};Object.entries(u).forEach(([k,v])=>{localStorage.setItem(k,v);sessionStorage.setItem(k,v)});window.isPremium=()=>true;window.isVip=()=>true;window.hasUnlimited=()=>true;document.querySelectorAll('[class*="顶级"],[class*="解锁"],[class*="Ultra"]').forEach(e=>e.style.display='none');document.querySelectorAll('[disabled]').forEach(e=>{e.removeAttribute('disabled');e.style.pointerEvents='auto'});if(typeof chrome!=='undefined'&&chrome.storage){chrome.storage.local.set({vip:true,premium:true,unlimited:true,user_level:999})}console.log('✅ 会译插件静默解锁完成')}catch(e){console.error('解锁失败:',e)}})();

// 持续监控版本（自动维护解锁状态）：
javascript:(function(){if(window.huiyiUnlockActive)return;window.huiyiUnlockActive=true;const unlockData={vip:'true',premium:'true',unlimited:'true',user_level:'999',subscription_status:'active',premium_expires:'*************'};function applyUnlock(){Object.entries(unlockData).forEach(([k,v])=>{localStorage.setItem(k,v);sessionStorage.setItem(k,v)});window.isPremium=()=>true;window.isVip=()=>true;window.hasUnlimited=()=>true;document.querySelectorAll('[class*="顶级"],[class*="解锁"],[class*="Ultra"]').forEach(e=>e.style.display='none');document.querySelectorAll('[disabled]').forEach(e=>{e.removeAttribute('disabled');e.style.pointerEvents='auto'})}applyUnlock();setInterval(applyUnlock,2000);const observer=new MutationObserver(applyUnlock);observer.observe(document.body,{childList:true,subtree:true});console.log('🔄 会译插件持续解锁已启动');alert('🔄 持续解锁模式已启动\n将自动维护解锁状态')})();

/*
使用说明：
1. 复制上面任意一个 javascript: 开头的代码
2. 在浏览器中创建新书签
3. 将复制的代码粘贴到书签的URL地址栏
4. 保存书签
5. 在会译插件页面点击该书签即可一键解锁

推荐使用：
- 标准版本：适合大多数用户，有成功提示
- 增强版本：功能最全面，解锁效果最好
- 静默版本：无弹窗，适合不想被打扰的用户
- 持续监控版本：自动维护解锁状态，适合长期使用

注意事项：
- 请在会译插件的页面使用书签
- 如果解锁失败，请刷新页面后重试
- 某些浏览器可能会阻止书签执行JavaScript，请在设置中允许
*/
