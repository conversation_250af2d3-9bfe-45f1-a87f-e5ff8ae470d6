// 会译插件完全解锁脚本 v4.0 - 同时解除登录限制和模型使用限制
// 让插件无需登录即可免费使用所有高级AI模型功能

(function() {
    'use strict';
    
    console.log('🚀 会译插件完全解锁脚本 v4.0 启动...');
    
    // 1. 综合用户状态设置 - 同时解决登录和会员限制
    function setCompleteUserStatus() {
        const completeUserData = {
            // === 登录状态相关 ===
            user_id: 'premium_user_12345',
            username: 'premium_user',
            email: '<EMAIL>',
            avatar: 'https://example.com/premium_avatar.jpg',
            
            // 登录验证
            logged_in: 'true',
            is_logged_in: 'true',
            login_status: 'active',
            auth_status: 'authenticated',
            session_valid: 'true',
            authenticated: 'true',
            
            // === 会员权限相关 ===
            // 基础会员状态
            premium: 'true',
            vip: 'true',
            pro: 'true',
            unlimited: 'true',
            paid: 'true',
            
            // 用户等级
            user_level: '999',
            user_type: 'premium',
            account_type: 'premium',
            membership: 'premium',
            membership_level: '999',
            tier: 'premium',
            plan: 'unlimited',
            
            // 订阅状态
            subscription: 'active',
            subscription_status: 'active',
            membership_status: 'active',
            account_status: 'active',
            
            // === Token和配额相关 ===
            token_balance: '999999',
            token_limit: '999999',
            token_quota: '999999',
            daily_tokens: '999999',
            monthly_tokens: '999999',
            remaining_tokens: '999999',
            unlimited_tokens: 'true',
            
            // 使用限制
            daily_limit: '999999',
            monthly_limit: '999999',
            usage_limit: '999999',
            request_limit: '999999',
            
            // === 过期时间相关 ===
            login_expires: '*************',
            session_expires: '*************',
            premium_expires: '*************',
            vip_expires: '*************',
            subscription_expires: '*************',
            token_reset_time: '*************',
            
            // === 功能权限相关 ===
            can_use_gpt4: 'true',
            can_use_claude: 'true',
            can_use_premium_models: 'true',
            unlimited_access: 'true',
            full_access: 'true',
            
            // === 其他状态 ===
            first_login: 'false',
            trial_used: 'false',
            restrictions: 'false',
            limited: 'false',
            blocked: 'false'
        };
        
        // 设置到所有可能的存储位置
        Object.entries(completeUserData).forEach(([key, value]) => {
            localStorage.setItem(key, value);
            sessionStorage.setItem(key, value);
            
            // 设置到全局变量
            window[key] = value === 'true' ? true : 
                         value === 'false' ? false : 
                         isNaN(value) ? value : parseInt(value);
        });
        
        // 设置用户对象
        window.currentUser = completeUserData;
        window.userInfo = completeUserData;
        window.user = completeUserData;
        window.authUser = completeUserData;
        
        console.log('✅ 完整用户状态设置完成');
    }
    
    // 2. 全面的函数拦截 - 登录检查和权限检查
    function interceptAllChecks() {
        // 登录检查函数
        const loginFunctions = [
            'isLoggedIn', 'checkLogin', 'verifyLogin', 'validateUser',
            'getUserStatus', 'checkAuth', 'isAuthenticated', 'hasAuth',
            'checkSession', 'validateSession', 'requireLogin'
        ];
        
        // 权限检查函数
        const permissionFunctions = [
            'isPremium', 'isVip', 'isPro', 'hasUnlimited', 'checkPremium',
            'checkVip', 'checkSubscription', 'hasPermission', 'canUse',
            'checkLimit', 'validateAccess', 'hasAccess'
        ];
        
        // 重写所有检查函数
        [...loginFunctions, ...permissionFunctions].forEach(funcName => {
            // 全局函数
            window[funcName] = function() {
                console.log(`拦截检查函数: ${funcName} -> 返回true`);
                return true;
            };
            
            // 对象方法
            ['app', 'huiyi', 'user', 'auth', 'login', 'premium', 'subscription'].forEach(objName => {
                if (window[objName] && typeof window[objName] === 'object') {
                    if (typeof window[objName][funcName] === 'function') {
                        window[objName][funcName] = function() {
                            console.log(`拦截对象方法: ${objName}.${funcName} -> 返回true`);
                            return true;
                        };
                    }
                }
            });
        });
        
        // 特殊的返回值函数
        window.getUserLevel = () => 999;
        window.getTokenBalance = () => 999999;
        window.getRemainingTokens = () => 999999;
        window.getSubscriptionStatus = () => 'active';
        window.getUserInfo = () => window.currentUser;
        
        console.log('✅ 全面函数拦截完成');
    }
    
    // 3. 网络请求完全控制 - 登录和权限验证
    function controlAllRequests() {
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = async function(url, options = {}) {
                const urlStr = url.toString().toLowerCase();
                
                // 检查是否是需要拦截的请求
                const shouldIntercept = [
                    'login', 'auth', 'user', 'session', 'verify',
                    'premium', 'vip', 'subscription', 'check', 'validate',
                    'limit', 'quota', 'token', 'permission'
                ].some(keyword => urlStr.includes(keyword));
                
                if (shouldIntercept) {
                    console.log('拦截关键请求:', url);
                    
                    // 返回完美的成功响应
                    const perfectResponse = {
                        success: true,
                        code: 200,
                        status: 'success',
                        message: 'Success',
                        data: {
                            // 用户信息
                            user_id: 'premium_user_12345',
                            username: 'premium_user',
                            email: '<EMAIL>',
                            
                            // 登录状态
                            logged_in: true,
                            authenticated: true,
                            session_valid: true,
                            
                            // 权限状态
                            premium: true,
                            vip: true,
                            unlimited: true,
                            user_level: 999,
                            subscription: 'active',
                            
                            // Token信息
                            token_balance: 999999,
                            remaining_tokens: 999999,
                            daily_limit: 999999,
                            
                            // 权限列表
                            permissions: ['all', 'premium', 'vip', 'unlimited'],
                            features: ['gpt4', 'claude', 'all_models'],
                            
                            // 过期时间
                            expires: *************,
                            token: 'fake_jwt_token_premium_user'
                        }
                    };
                    
                    return new Response(JSON.stringify(perfectResponse), {
                        status: 200,
                        statusText: 'OK',
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                
                return originalFetch(url, options);
            };
        }
        
        console.log('✅ 网络请求控制完成');
    }
    
    // 4. UI完全控制 - 隐藏所有限制提示
    function controlCompleteUI() {
        const completeUIStyle = document.createElement('style');
        completeUIStyle.id = 'huiyi-complete-unlock';
        completeUIStyle.textContent = `
            /* 隐藏登录相关UI */
            [class*="login"], [class*="登录"], [class*="sign-in"], [class*="signin"],
            [class*="auth"], [class*="authenticate"], [id*="login"], [id*="登录"] {
                display: none !important;
            }
            
            /* 隐藏限制相关UI */
            [class*="limit"], [class*="restrict"], [class*="premium"], [class*="upgrade"],
            [class*="vip"], [class*="顶级"], [class*="解锁"], [class*="Ultra"] {
                display: none !important;
            }
            
            /* 隐藏所有弹窗和遮罩 */
            [class*="modal"], [class*="popup"], [class*="dialog"], [class*="overlay"],
            [class*="mask"], [class*="cover"], [class*="block"] {
                display: none !important;
            }
            
            /* 确保所有功能可见和可用 */
            [class*="content"], [class*="main"], [class*="function"], [class*="feature"],
            [class*="model"], [class*="option"], [class*="select"] {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }
            
            /* 移除禁用状态 */
            .disabled, [disabled], [class*="disabled"] {
                pointer-events: auto !important;
                opacity: 1 !important;
                cursor: pointer !important;
            }
        `;
        
        const head = document.head || document.getElementsByTagName('head')[0];
        if (head) {
            head.appendChild(completeUIStyle);
        }
        
        // 持续监控页面变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        const text = node.textContent || '';
                        const className = node.className || '';
                        
                        // 隐藏限制相关元素
                        const restrictionKeywords = [
                            '登录', '请先登录', '需要登录', 'Login', 'Sign in',
                            '顶级模型', '解锁', '升级', 'Premium', 'VIP', 'Ultra'
                        ];
                        
                        if (restrictionKeywords.some(keyword => text.includes(keyword)) ||
                            ['login', 'auth', 'premium', 'limit'].some(cls => className.includes(cls))) {
                            node.style.display = 'none';
                        }
                        
                        // 启用功能元素
                        const functionalElements = node.querySelectorAll('button, [class*="model"], [class*="option"]');
                        functionalElements.forEach(el => {
                            el.style.pointerEvents = 'auto';
                            el.style.opacity = '1';
                            el.removeAttribute('disabled');
                        });
                    }
                });
            });
        });
        
        if (document.body) {
            observer.observe(document.body, { childList: true, subtree: true });
        }
        
        console.log('✅ UI完全控制完成');
    }
    
    // 5. 存储完全控制 - 确保状态持久化
    function controlCompleteStorage() {
        const originalSetItem = localStorage.setItem;
        const originalGetItem = localStorage.getItem;
        
        localStorage.setItem = function(key, value) {
            const keyLower = key.toLowerCase();
            
            // 拦截可能破坏解锁状态的设置
            if (keyLower.includes('login') || keyLower.includes('auth') || 
                keyLower.includes('premium') || keyLower.includes('vip') ||
                keyLower.includes('limit') || keyLower.includes('restrict')) {
                
                // 强制设置为解锁状态
                if (keyLower.includes('login') || keyLower.includes('auth') ||
                    keyLower.includes('premium') || keyLower.includes('vip') ||
                    keyLower.includes('unlimited')) {
                    value = 'true';
                }
                
                if (keyLower.includes('level')) {
                    value = '999';
                }
                
                if (keyLower.includes('limit') || keyLower.includes('restrict')) {
                    value = keyLower.includes('unlimited') ? 'true' : '999999';
                }
            }
            
            return originalSetItem.call(this, key, value);
        };
        
        localStorage.getItem = function(key) {
            const value = originalGetItem.call(this, key);
            const keyLower = key.toLowerCase();
            
            // 强制返回解锁状态
            if (keyLower.includes('logged_in') || keyLower.includes('authenticated') ||
                keyLower.includes('premium') || keyLower.includes('vip') ||
                keyLower.includes('unlimited')) {
                return 'true';
            }
            
            if (keyLower.includes('level')) {
                return '999';
            }
            
            if (keyLower.includes('token') && keyLower.includes('balance')) {
                return '999999';
            }
            
            if (keyLower.includes('status') && 
                (keyLower.includes('subscription') || keyLower.includes('auth'))) {
                return 'active';
            }
            
            return value;
        };
        
        console.log('✅ 存储完全控制完成');
    }
    
    // 6. 主执行函数
    function executeCompleteUnlock() {
        console.log('🚀 开始执行完全解锁...');
        
        setCompleteUserStatus();
        interceptAllChecks();
        controlAllRequests();
        controlCompleteUI();
        controlCompleteStorage();
        
        // Chrome扩展存储
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const extensionData = {
                logged_in: true, premium: true, vip: true, unlimited: true,
                user_level: 999, subscription_status: 'active',
                token_balance: 999999, authenticated: true
            };
            
            chrome.storage.local.set(extensionData);
            chrome.storage.sync.set(extensionData);
        }
        
        // 等待DOM加载完成后再次执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    controlCompleteUI();
                    setCompleteUserStatus();
                }, 500);
            });
        }
        
        // 延迟执行确保完全生效
        setTimeout(() => {
            controlCompleteUI();
            setCompleteUserStatus();
        }, 2000);
        
        setTimeout(() => {
            controlCompleteUI();
        }, 5000);
        
        console.log('🎉 完全解锁执行完成！');
        console.log('现在可以无需登录免费使用所有高级AI模型功能！');
    }
    
    // 立即执行
    executeCompleteUnlock();
    
    // 定期维护解锁状态
    setInterval(() => {
        setCompleteUserStatus();
    }, 15000);
    
    // 暴露手动执行函数
    window.huiyiCompleteUnlock = executeCompleteUnlock;
    
    console.log('🚀 完全解锁脚本安装完成！');
    
})();
