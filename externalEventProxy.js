"use strict";(()=>{var m=Object.create;var v=Object.defineProperty;var y=Object.getOwnPropertyDescriptor;var f=Object.getOwnPropertyNames;var b=Object.getPrototypeOf,w=Object.prototype.hasOwnProperty;var k=(t,e,n)=>e in t?v(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var L=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var I=(t,e,n,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of f(e))!w.call(t,a)&&a!==n&&v(t,a,{get:()=>e[a],enumerable:!(i=y(e,a))||i.enumerable});return t};var o=(t,e,n)=>(n=t!=null?m(b(t)):{},I(e||!t||!t.__esModule?v(n,"default",{value:t,enumerable:!0}):n,t));var d=(t,e,n)=>(k(t,typeof e!="symbol"?e+"":e,n),n);var r=L(()=>{"use strict";globalThis&&(globalThis.__huiyi_envvars__={host:"https://huiyiai.net",browser:"edge",oem:"default",buildMode:"pack"})});var X=o(r(),1);var R=o(r(),1);var h=class{constructor(){d(this,"envvars",globalThis.__huiyi_envvars__);d(this,"_isBackground",!1);d(this,"_isSiderPanel",!1)}get extensionPrefix(){return"huiyi"}get productName(){return"\u4F1A\u8BD1"}get browser(){return this.envvars.browser}get buildMode(){return this.envvars.buildMode}get browserDisplayName(){return this.envvars.browser==="edge"?"Edge":"Chrome"}get isBackground(){return this._isBackground}get isSiderPanel(){return this._isSiderPanel}get host(){return this.envvars.host}get oem(){return this.envvars.oem}get apiBasePath(){return"/hy_api"}get isFirefox(){return this.browser==="firefox"}markIsBackground(){this._isBackground=!0}markIsSiderPanel(){this._isSiderPanel=!0}withExtensionPrefix(e){return`${this.extensionPrefix}-${e}`}},E=new h;var N=o(r());var H=o(r()),u,_=new Uint8Array(16);function c(){if(!u&&(u=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!u))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return u(_)}var A=o(r());var s=[];for(let t=0;t<256;++t)s.push((t+256).toString(16).slice(1));function g(t,e=0){return(s[t[e+0]]+s[t[e+1]]+s[t[e+2]]+s[t[e+3]]+"-"+s[t[e+4]]+s[t[e+5]]+"-"+s[t[e+6]]+s[t[e+7]]+"-"+s[t[e+8]]+s[t[e+9]]+"-"+s[t[e+10]]+s[t[e+11]]+s[t[e+12]]+s[t[e+13]]+s[t[e+14]]+s[t[e+15]]).toLowerCase()}var J=o(r());var j=o(r()),S=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),p={randomUUID:S};function T(t,e,n){if(p.randomUUID&&!e&&!t)return p.randomUUID();t=t||{};let i=t.random||(t.rng||c)();if(i[6]=i[6]&15|64,i[8]=i[8]&63|128,e){n=n||0;for(let a=0;a<16;++a)e[n+a]=i[a];return e}return g(i)}var l=T;var x=class{constructor(){d(this,"socketId",l());d(this,"extensionId","");d(this,"extensionEventListeners",{});this.init()}init(){if(!["huiyiai.net","huiyidev.miemie.la"].includes(window.location.hostname))return;window.location.hostname;let n=document.body.getAttribute(`${E.withExtensionPrefix("id")}`);this.extensionId=n,window.ptLenovoEventProxy={sendEvent:this.sendEvent.bind(this),listenExtensionEvent:this.listenExtensionEvent.bind(this),unListenExtensionEvent:this.unListenExtensionEvent.bind(this)},this.establishEventSocket()}sendEvent(e,n){return new Promise(i=>{chrome.runtime.sendMessage(this.extensionId,{type:e,data:n},a=>{i(a)})})}listenExtensionEvent(e,n){this.extensionEventListeners[e]||(this.extensionEventListeners[e]=[]);let i=l();return this.extensionEventListeners[e].push({id:i,eventName:e,callback:n}),i}unListenExtensionEvent(e,n){this.extensionEventListeners[e]&&(this.extensionEventListeners[e]=this.extensionEventListeners[e].filter(i=>i.id!==n))}establishEventSocket(){this.socketId,this.sendEvent("establishExternalSocket",{socketId:this.socketId}).then(e=>{try{let{event:n,data:i}=e;this.handleExtensionEvent(n,i)}catch(n){console.error(n,e)}this.establishEventSocket()}).catch(()=>{setTimeout(()=>{this.establishEventSocket()},1e3)})}handleExtensionEvent(e,n){if(this.extensionEventListeners[e])for(let i of this.extensionEventListeners[e])i.callback(n)}};new x;})();
