// 会译插件登录绕过脚本 v1.0 - 专门解除登录使用限制
// 让插件无需登录即可正常使用所有功能

(function() {
    'use strict';
    
    console.log('🔐 会译插件登录绕过脚本启动...');
    
    // 1. 伪造用户登录状态
    function fakeUserLoginStatus() {
        // 设置虚假的用户信息
        const fakeUserData = {
            // 基础用户信息
            user_id: 'fake_user_12345',
            username: 'premium_user',
            email: '<EMAIL>',
            avatar: 'https://example.com/avatar.jpg',
            
            // 登录状态
            logged_in: 'true',
            is_logged_in: 'true',
            login_status: 'active',
            auth_status: 'authenticated',
            session_valid: 'true',
            
            // 用户等级和权限
            user_level: '999',
            user_type: 'premium',
            account_type: 'premium',
            membership: 'premium',
            subscription: 'active',
            
            // 高级功能权限
            premium: 'true',
            vip: 'true',
            pro: 'true',
            unlimited: 'true',
            
            // Token和配额
            token_balance: '999999',
            daily_quota: '999999',
            monthly_quota: '999999',
            
            // 过期时间
            login_expires: '*************',
            session_expires: '*************',
            premium_expires: '*************'
        };
        
        // 设置到localStorage
        Object.entries(fakeUserData).forEach(([key, value]) => {
            localStorage.setItem(key, value);
            sessionStorage.setItem(key, value);
        });
        
        // 设置到全局变量
        window.currentUser = fakeUserData;
        window.userInfo = fakeUserData;
        window.user = fakeUserData;
        
        console.log('✅ 虚假用户登录状态设置完成');
    }
    
    // 2. 拦截登录检查函数
    function interceptLoginChecks() {
        // 常见的登录检查函数名
        const loginCheckFunctions = [
            'isLoggedIn', 'checkLogin', 'verifyLogin', 'validateUser',
            'getUserStatus', 'checkAuth', 'isAuthenticated', 'hasAuth',
            'checkSession', 'validateSession', 'isUserValid', 'checkUser'
        ];
        
        // 重写这些函数返回已登录状态
        loginCheckFunctions.forEach(funcName => {
            if (window[funcName]) {
                window[funcName] = function() {
                    console.log(`拦截登录检查函数: ${funcName}`);
                    return true;
                };
            }
            
            // 也尝试在常见的对象中查找
            ['app', 'huiyi', 'user', 'auth', 'login'].forEach(objName => {
                if (window[objName] && typeof window[objName] === 'object') {
                    if (window[objName][funcName]) {
                        window[objName][funcName] = function() {
                            console.log(`拦截对象方法: ${objName}.${funcName}`);
                            return true;
                        };
                    }
                }
            });
        });
        
        // 创建通用的登录检查函数
        window.isLoggedIn = () => true;
        window.checkLogin = () => true;
        window.getUserInfo = () => window.currentUser;
        window.getAuthStatus = () => 'authenticated';
        
        console.log('✅ 登录检查函数拦截完成');
    }
    
    // 3. 拦截登录相关的网络请求
    function interceptLoginRequests() {
        // 拦截fetch请求
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = async function(url, options = {}) {
                const urlStr = url.toString().toLowerCase();
                
                // 检查是否是登录相关请求
                if (urlStr.includes('login') || urlStr.includes('auth') || 
                    urlStr.includes('user') || urlStr.includes('session')) {
                    
                    console.log('拦截登录相关请求:', url);
                    
                    // 返回虚假的成功登录响应
                    const fakeResponse = {
                        success: true,
                        code: 200,
                        message: 'Login successful',
                        data: {
                            user_id: 'fake_user_12345',
                            username: 'premium_user',
                            email: '<EMAIL>',
                            premium: true,
                            vip: true,
                            unlimited: true,
                            user_level: 999,
                            subscription: 'active',
                            token: 'fake_jwt_token_12345',
                            expires: *************
                        }
                    };
                    
                    return new Response(JSON.stringify(fakeResponse), {
                        status: 200,
                        statusText: 'OK',
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                
                // 其他请求正常处理
                return originalFetch(url, options);
            };
        }
        
        // 拦截XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._interceptUrl = url;
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            const url = xhr._interceptUrl;
            
            if (url && (url.includes('login') || url.includes('auth') || url.includes('user'))) {
                console.log('拦截XHR登录请求:', url);
                
                // 模拟成功响应
                setTimeout(() => {
                    const fakeResponse = {
                        success: true,
                        code: 200,
                        data: {
                            user_id: 'fake_user_12345',
                            premium: true,
                            vip: true,
                            unlimited: true,
                            user_level: 999
                        }
                    };
                    
                    Object.defineProperty(xhr, 'status', { value: 200 });
                    Object.defineProperty(xhr, 'readyState', { value: 4 });
                    Object.defineProperty(xhr, 'responseText', { 
                        value: JSON.stringify(fakeResponse) 
                    });
                    
                    if (xhr.onload) xhr.onload();
                    if (xhr.onreadystatechange) xhr.onreadystatechange();
                }, 100);
                
                return;
            }
            
            return originalSend.apply(this, arguments);
        };
        
        console.log('✅ 登录请求拦截完成');
    }
    
    // 4. 隐藏登录相关的UI元素
    function hideLoginUI() {
        const loginUIStyle = document.createElement('style');
        loginUIStyle.id = 'huiyi-login-bypass';
        loginUIStyle.textContent = `
            /* 隐藏登录相关UI */
            [class*="login"], [class*="登录"], [class*="sign-in"], [class*="signin"],
            [class*="auth"], [class*="authenticate"], [id*="login"], [id*="登录"],
            [id*="sign-in"], [id*="signin"], [id*="auth"] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
            
            /* 隐藏登录弹窗和遮罩 */
            [class*="modal"][class*="login"], [class*="popup"][class*="login"],
            [class*="dialog"][class*="login"], [class*="overlay"][class*="login"] {
                display: none !important;
            }
            
            /* 显示所有功能区域 */
            [class*="content"], [class*="main"], [class*="function"], [class*="feature"] {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }
            
            /* 移除登录要求的遮罩 */
            [class*="mask"], [class*="cover"], [class*="block"] {
                display: none !important;
            }
        `;
        
        const head = document.head || document.getElementsByTagName('head')[0];
        if (head) {
            head.appendChild(loginUIStyle);
        }
        
        // 持续监控并隐藏新出现的登录UI
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        const text = node.textContent || '';
                        const className = node.className || '';
                        
                        // 检查是否包含登录相关文本或类名
                        if (text.includes('登录') || text.includes('请先登录') || 
                            text.includes('需要登录') || text.includes('Login') ||
                            className.includes('login') || className.includes('auth')) {
                            node.style.display = 'none';
                        }
                        
                        // 查找子元素中的登录UI
                        const loginElements = node.querySelectorAll('[class*="login"], [class*="登录"], [class*="auth"]');
                        loginElements.forEach(el => el.style.display = 'none');
                    }
                });
            });
        });
        
        if (document.body) {
            observer.observe(document.body, { childList: true, subtree: true });
        }
        
        console.log('✅ 登录UI隐藏完成');
    }
    
    // 5. 重写存储操作，确保登录状态持久化
    function persistLoginStatus() {
        const originalSetItem = localStorage.setItem;
        const originalGetItem = localStorage.getItem;
        
        localStorage.setItem = function(key, value) {
            // 拦截可能清除登录状态的操作
            if (key.toLowerCase().includes('login') || 
                key.toLowerCase().includes('auth') || 
                key.toLowerCase().includes('user')) {
                
                // 如果是设置为false或空值，强制改为true
                if (value === 'false' || value === '' || value === null || value === 'null') {
                    if (key.toLowerCase().includes('logged') || 
                        key.toLowerCase().includes('auth') ||
                        key.toLowerCase().includes('login')) {
                        value = 'true';
                    }
                }
            }
            
            return originalSetItem.call(this, key, value);
        };
        
        localStorage.getItem = function(key) {
            const value = originalGetItem.call(this, key);
            
            // 强制返回登录状态
            if (key.toLowerCase().includes('logged_in') || 
                key.toLowerCase().includes('is_logged_in') ||
                key.toLowerCase().includes('login_status')) {
                return 'true';
            }
            
            if (key.toLowerCase().includes('auth_status')) {
                return 'authenticated';
            }
            
            if (key.toLowerCase().includes('session_valid')) {
                return 'true';
            }
            
            return value;
        };
        
        console.log('✅ 登录状态持久化完成');
    }
    
    // 6. 主执行函数
    function executeLoginBypass() {
        console.log('🔐 开始执行登录绕过...');
        
        fakeUserLoginStatus();
        interceptLoginChecks();
        interceptLoginRequests();
        hideLoginUI();
        persistLoginStatus();
        
        // 等待DOM加载完成后再次执行UI相关操作
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    hideLoginUI();
                    fakeUserLoginStatus();
                }, 500);
            });
        }
        
        // 延迟执行，确保捕获异步加载的内容
        setTimeout(() => {
            hideLoginUI();
            fakeUserLoginStatus();
        }, 2000);
        
        setTimeout(() => {
            hideLoginUI();
        }, 5000);
        
        console.log('🎉 登录绕过完成！现在可以无需登录使用所有功能');
    }
    
    // 立即执行
    executeLoginBypass();
    
    // 定期维护登录状态
    setInterval(() => {
        fakeUserLoginStatus();
    }, 10000);
    
    // 暴露手动执行函数
    window.huiyiLoginBypass = executeLoginBypass;
    
    console.log('🔐 登录绕过脚本安装完成！');
    
})();
