{"action": {"default_icon": {"128": "static/default/logo-transparent.png", "16": "static/default/logo-transparent.png", "32": "static/default/logo-transparent.png", "48": "static/default/logo-transparent.png"}}, "background": {"service_worker": "background.js"}, "content_scripts": [{"all_frames": true, "js": ["videoLoad.js"], "matches": ["https://youtube.com/*", "https://m.youtube.com/*", "https://www.youtube.com/*"], "run_at": "document_start"}, {"all_frames": true, "js": ["content.js"], "matches": ["<all_urls>"], "run_at": "document_start"}], "description": "一站式的免费AI翻译工具，由DeepL和DeepSeek双核翻译引擎驱动。提供沉浸式的网页双语对照翻译体验，支持PDF翻译（完美还原排版），视频双语字幕翻译（Youtube，B站），AI划词翻译（带音标及自然发音），截图翻译，在线翻译等全场景的翻译服务。", "externally_connectable": {"matches": ["https://huiyiai.net/*"]}, "host_permissions": ["<all_urls>"], "icons": {"128": "static/default/logo-transparent.png", "16": "static/default/logo-transparent.png", "32": "static/default/logo-transparent.png", "48": "static/default/logo-transparent.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmZOSwHiHzLkajWKaNBi2Lr4JZAFXGGLJoVdyehAnOQEsiaqYEjZIda8qQ7x6imC+sg/dV77ipF/ix/hoZkApZXZTEgWsET1mfYikKaJIQxPVGkvRLjDrw++8z7VNAlMJUSPoFeBVXQE6wsoNMhSBhxB4R7MjVfIbz6XVV+kwtW5iZQs2gG6q4YO5gi7IGxSSNE6UwT9SHOx/fPBtKoFzm6n6guScLOIGhN1lBzrqHLS4F+ivJh4Hs/BPHYsUMXjakhFKzBLnbDKG0Ry0QSs+z/AGG6AO8KY9ypFEHec1A2S6W7r8UB+PtjBpEm91BHOAyVUr8C83Ga5lXHkilNXFeQIDAQAB", "manifest_version": 3, "name": "会译:一站式 AI 翻译｜免费", "options_page": "option.html", "permissions": ["storage", "unlimitedStorage", "scripting", "proxy", "tts", "contextMenus", "webRequest", "sidePanel"], "side_panel": {"default_path": "sidePanel.html"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "2.1.0", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["content.css"]}, {"matches": ["<all_urls>"], "resources": ["content.js"]}, {"matches": ["<all_urls>"], "resources": ["static/*"]}, {"matches": ["<all_urls>"], "resources": ["externalEventProxy.js"]}, {"matches": ["<all_urls>"], "resources": ["removeChildPatch.js"]}, {"matches": ["<all_urls>"], "resources": ["videoLoad.js"]}, {"matches": ["<all_urls>"], "resources": ["video.js"]}]}