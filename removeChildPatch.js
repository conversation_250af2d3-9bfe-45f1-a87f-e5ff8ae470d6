"use strict";(()=>{var p=Object.create;var s=Object.defineProperty;var f=Object.getOwnPropertyDescriptor;var d=Object.getOwnPropertyNames;var l=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty;var u=(o,e)=>()=>(e||o((e={exports:{}}).exports,e),e.exports);var y=(o,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of d(e))!h.call(o,n)&&n!==t&&s(o,n,{get:()=>e[n],enumerable:!(r=f(e,n))||r.enumerable});return o};var a=(o,e,t)=>(t=o!=null?p(l(o)):{},y(e||!o||!o.__esModule?s(t,"default",{value:o,enumerable:!0}):t,o));var i=u(()=>{"use strict";globalThis&&(globalThis.__huiyi_envvars__={host:"https://huiyiai.net",browser:"edge",oem:"default",buildMode:"pack"})});var c=a(i(),1);if(typeof Node=="function"&&Node.prototype){let o=Node.prototype.removeChild;Node.prototype.removeChild=function(t){return t.parentNode!==this?(console&&console.error("Cannot remove a child from a different parent",t,this),t):o.apply(this,[t])};let e=Node.prototype.insertBefore;Node.prototype.insertBefore=function(t,r){return r&&r.parentNode!==this?(console&&console.error("Cannot insert before a reference node from a different parent",r,this),t):e.apply(this,[t,r])}}})();
