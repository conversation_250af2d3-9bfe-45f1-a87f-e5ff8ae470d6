@echo off
chcp 65001 >nul
title 会译插件超级解锁工具 v3.0
color 0A

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    会译插件超级解锁工具 v3.0                    ║
echo ║                      一键解除所有模型限制                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/6] 正在关闭浏览器进程...
taskkill /f /im chrome.exe 2>nul
taskkill /f /im msedge.exe 2>nul
taskkill /f /im firefox.exe 2>nul
taskkill /f /im brave.exe 2>nul
taskkill /f /im opera.exe 2>nul
echo     所有浏览器进程已关闭

echo [2/6] 等待进程完全关闭...
timeout /t 3 /nobreak >nul

echo [3/6] 正在备份原始文件...
if not exist "backup" mkdir backup
copy background.js backup\background.js.bak 2>nul
copy content.js backup\content.js.bak 2>nul
copy popup.js backup\popup.js.bak 2>nul
copy sidePanel.js backup\sidePanel.js.bak 2>nul
copy option.js backup\option.js.bak 2>nul
copy manifest.json backup\manifest.json.bak 2>nul
echo     原始文件备份完成

echo [4/6] 正在清理浏览器存储和缓存...
:: 清理Chrome相关存储
powershell -Command "& {
    $paths = @(
        '$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Local Storage',
        '$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Session Storage',
        '$env:LOCALAPPDATA\Google\Chrome\User Data\Default\IndexedDB',
        '$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Local Storage',
        '$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Session Storage',
        '$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\IndexedDB'
    )
    
    foreach ($path in $paths) {
        $expandedPath = [Environment]::ExpandEnvironmentVariables($path)
        if (Test-Path $expandedPath) {
            try {
                Remove-Item $expandedPath -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host '已清理: ' $expandedPath
            } catch {}
        }
    }
}" 2>nul
echo     浏览器存储清理完成

echo [5/6] 正在注入超级解锁代码...

:: 创建超级解锁代码
echo // 会译插件超级解锁代码 v3.0 > super_unlock_inject.js
echo (function() { >> super_unlock_inject.js
echo     'use strict'; >> super_unlock_inject.js
echo     console.log('🚀 会译插件超级解锁 v3.0 已激活'); >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     // 1. 强制设置所有解锁状态 >> super_unlock_inject.js
echo     const unlockData = { >> super_unlock_inject.js
echo         vip: 'true', premium: 'true', pro: 'true', unlimited: 'true', >> super_unlock_inject.js
echo         user_level: '999', subscription_status: 'active', >> super_unlock_inject.js
echo         premium_expires: '9999999999999', token_limit: '999999', >> super_unlock_inject.js
echo         daily_limit: '999999', remaining_tokens: '999999' >> super_unlock_inject.js
echo     }; >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     // 2. 设置localStorage >> super_unlock_inject.js
echo     Object.entries(unlockData).forEach(([key, value]) =^> { >> super_unlock_inject.js
echo         try { >> super_unlock_inject.js
echo             localStorage.setItem(key, value); >> super_unlock_inject.js
echo             sessionStorage.setItem(key, value); >> super_unlock_inject.js
echo         } catch(e) {} >> super_unlock_inject.js
echo     }); >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     // 3. 设置全局变量 >> super_unlock_inject.js
echo     window.isPremium = () =^> true; >> super_unlock_inject.js
echo     window.isVip = () =^> true; >> super_unlock_inject.js
echo     window.hasUnlimited = () =^> true; >> super_unlock_inject.js
echo     window.getUserLevel = () =^> 999; >> super_unlock_inject.js
echo     window.checkSubscription = () =^> ({status: 'active', level: 999}); >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     // 4. 拦截存储操作 >> super_unlock_inject.js
echo     const originalSetItem = localStorage.setItem; >> super_unlock_inject.js
echo     localStorage.setItem = function(key, value) { >> super_unlock_inject.js
echo         if (key.includes('premium') ^|^| key.includes('vip') ^|^| key.includes('unlimited')) { >> super_unlock_inject.js
echo             value = 'true'; >> super_unlock_inject.js
echo         } >> super_unlock_inject.js
echo         if (key.includes('level')) value = '999'; >> super_unlock_inject.js
echo         if (key.includes('limit') ^&^& !key.includes('unlimited')) value = '999999'; >> super_unlock_inject.js
echo         return originalSetItem.call(this, key, value); >> super_unlock_inject.js
echo     }; >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     // 5. Chrome扩展存储 >> super_unlock_inject.js
echo     if (typeof chrome !== 'undefined' ^&^& chrome.storage) { >> super_unlock_inject.js
echo         const storageData = {}; >> super_unlock_inject.js
echo         Object.entries(unlockData).forEach(([key, value]) =^> { >> super_unlock_inject.js
echo             storageData[key] = value === 'true' ? true : (isNaN(value) ? value : parseInt(value)); >> super_unlock_inject.js
echo         }); >> super_unlock_inject.js
echo         chrome.storage.local.set(storageData); >> super_unlock_inject.js
echo         chrome.storage.sync.set(storageData); >> super_unlock_inject.js
echo     } >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     // 6. 持续维护解锁状态 >> super_unlock_inject.js
echo     setInterval(() =^> { >> super_unlock_inject.js
echo         Object.entries(unlockData).forEach(([key, value]) =^> { >> super_unlock_inject.js
echo             localStorage.setItem(key, value); >> super_unlock_inject.js
echo         }); >> super_unlock_inject.js
echo     }, 2000); >> super_unlock_inject.js
echo. >> super_unlock_inject.js
echo     console.log('✅ 超级解锁代码注入完成'); >> super_unlock_inject.js
echo })(); >> super_unlock_inject.js

:: 注入到所有主要文件
echo     正在修改 background.js...
type super_unlock_inject.js > temp_bg.js
type background.js >> temp_bg.js
move temp_bg.js background.js

echo     正在修改 content.js...
type super_unlock_inject.js > temp_content.js
type content.js >> temp_content.js
move temp_content.js content.js

echo     正在修改 popup.js...
type super_unlock_inject.js > temp_popup.js
type popup.js >> temp_popup.js
move temp_popup.js popup.js

echo     正在修改 sidePanel.js...
type super_unlock_inject.js > temp_side.js
type sidePanel.js >> temp_side.js
move temp_side.js sidePanel.js

echo     正在修改 option.js...
type super_unlock_inject.js > temp_option.js
type option.js >> temp_option.js
move temp_option.js option.js

echo [6/6] 正在修改插件权限...
powershell -Command "& {
    $manifestPath = 'manifest.json'
    if (Test-Path $manifestPath) {
        try {
            $manifest = Get-Content $manifestPath -Raw | ConvertFrom-Json
            
            # 添加所有必要权限
            $permissions = @('storage', 'unlimitedStorage', 'scripting', 'proxy', 'tts', 'contextMenus', 'webRequest', 'sidePanel', 'activeTab', 'tabs', 'background')
            $manifest.permissions = $permissions
            
            # 添加主机权限
            $manifest.host_permissions = @('<all_urls>')
            
            # 保存修改
            $manifest | ConvertTo-Json -Depth 10 | Set-Content $manifestPath -Encoding UTF8
            Write-Host '    manifest.json 权限扩展完成'
        } catch {
            Write-Host '    manifest.json 修改失败，请手动检查'
        }
    }
}" 2>nul

:: 清理临时文件
del super_unlock_inject.js 2>nul

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎉 解锁完成！                          ║
echo ║                                                              ║
echo ║  ✅ 所有原始文件已备份到 backup 目录                          ║
echo ║  ✅ 超级解锁代码已注入到所有主要文件                          ║
echo ║  ✅ 插件权限已扩展                                            ║
echo ║  ✅ 浏览器存储已清理                                          ║
echo ║                                                              ║
echo ║  📋 下一步操作：                                              ║
echo ║  1. 重新启动浏览器                                            ║
echo ║  2. 进入扩展管理页面                                          ║
echo ║  3. 重新加载会译插件                                          ║
echo ║  4. 享受无限制的AI模型！                                      ║
echo ║                                                              ║
echo ║  🔧 如需恢复原始状态：                                        ║
echo ║     copy backup\*.bak .                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 自动打开浏览器扩展页面
echo 正在自动打开浏览器扩展管理页面...
timeout /t 2 /nobreak >nul
start chrome://extensions/ 2>nul
start msedge://extensions/ 2>nul

echo.
echo 按任意键退出...
pause >nul
