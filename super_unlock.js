// 会译插件超级解锁脚本 v3.0 - 最强解锁方案
// 专门针对最新的限制检查机制和反调试技术

(function() {
    'use strict';
    
    console.log('⚡ 会译插件超级解锁脚本 v3.0 启动...');
    
    // 1. 高级反检测和隐身模式
    function advancedAntiDetection() {
        // 隐藏所有解锁痕迹
        const originalConsole = { ...console };
        
        // 创建静默模式
        const silentMode = true;
        if (silentMode) {
            ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
                console[method] = function(...args) {
                    const message = args.join(' ');
                    if (!message.includes('解锁') && !message.includes('unlock') && 
                        !message.includes('⚡') && !message.includes('🚀')) {
                        originalConsole[method].apply(console, args);
                    }
                };
            });
        }
        
        // 伪装成正常的插件功能
        window.huiyiTranslate = function() { return true; };
        window.huiyiConfig = { version: '2.1.0', loaded: true };
        
        // 删除可疑的全局变量
        delete window.huiyiUnlock;
        delete window.huiyiUltimateUnlock;
        delete window.huiyiForceUnlock;
        
        console.log('✅ 高级反检测启动');
    }
    
    // 2. 智能存储劫持 - 更聪明的存储操作
    function intelligentStorageHijack() {
        const unlockStates = {
            premium: true, vip: true, pro: true, unlimited: true,
            level: 999, tier: 'premium', plan: 'unlimited',
            subscription: 'active', status: 'active',
            expires: 9999999999999, limit: false, restricted: false
        };
        
        // 创建智能存储代理
        function createSmartProxy(storage) {
            const originalGetItem = storage.getItem;
            const originalSetItem = storage.setItem;
            
            storage.getItem = function(key) {
                const originalValue = originalGetItem.call(this, key);
                
                // 智能判断并返回解锁状态
                for (const [keyword, value] of Object.entries(unlockStates)) {
                    if (key.toLowerCase().includes(keyword)) {
                        return typeof value === 'boolean' ? value.toString() : value.toString();
                    }
                }
                
                return originalValue;
            };
            
            storage.setItem = function(key, value) {
                // 拦截并修改限制相关的设置
                for (const keyword of Object.keys(unlockStates)) {
                    if (key.toLowerCase().includes(keyword)) {
                        const unlockValue = unlockStates[keyword];
                        value = typeof unlockValue === 'boolean' ? unlockValue.toString() : unlockValue.toString();
                        break;
                    }
                }
                
                return originalSetItem.call(this, key, value);
            };
        }
        
        createSmartProxy(localStorage);
        createSmartProxy(sessionStorage);
        
        console.log('✅ 智能存储劫持完成');
    }
    
    // 3. 深度函数Hook - 拦截所有可能的检查函数
    function deepFunctionHook() {
        // Hook所有可能的检查函数
        const suspiciousPatterns = [
            /check.*premium/i, /verify.*vip/i, /validate.*subscription/i,
            /is.*premium/i, /has.*vip/i, /get.*level/i, /check.*limit/i
        ];
        
        // 扫描并Hook全局函数
        function hookGlobalFunctions(obj, path = '', depth = 0) {
            if (depth > 3) return; // 限制递归深度
            
            for (const key in obj) {
                try {
                    const value = obj[key];
                    if (typeof value === 'function') {
                        const funcStr = value.toString();
                        const shouldHook = suspiciousPatterns.some(pattern => 
                            pattern.test(key) || pattern.test(funcStr)
                        );
                        
                        if (shouldHook) {
                            const originalFunc = value;
                            obj[key] = function(...args) {
                                try {
                                    const result = originalFunc.apply(this, args);
                                    
                                    // 修改返回值为解锁状态
                                    if (typeof result === 'boolean') return true;
                                    if (typeof result === 'number') return 999;
                                    if (typeof result === 'object' && result !== null) {
                                        return { ...result, premium: true, vip: true, unlimited: true };
                                    }
                                    
                                    return result;
                                } catch (e) {
                                    return true; // 出错时假设有权限
                                }
                            };
                        }
                    } else if (typeof value === 'object' && value !== null) {
                        hookGlobalFunctions(value, `${path}.${key}`, depth + 1);
                    }
                } catch (e) {
                    // 忽略无法访问的属性
                }
            }
        }
        
        hookGlobalFunctions(window);
        console.log('✅ 深度函数Hook完成');
    }
    
    // 4. 网络请求完全控制
    function totalNetworkControl() {
        // 创建虚假的成功响应
        const createSuccessResponse = (originalData = {}) => ({
            ...originalData,
            success: true,
            premium: true,
            vip: true,
            unlimited: true,
            level: 999,
            subscription: 'active',
            status: 'active',
            expires: 9999999999999,
            remaining: 999999,
            limit: 999999,
            quota: 999999
        });
        
        // 完全控制fetch
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = async function(url, options = {}) {
                try {
                    const response = await originalFetch(url, options);
                    
                    // 检查是否是用户状态相关请求
                    const urlStr = url.toString().toLowerCase();
                    const statusKeywords = ['user', 'premium', 'vip', 'subscription', 'check', 'verify', 'validate'];
                    
                    if (statusKeywords.some(keyword => urlStr.includes(keyword))) {
                        const clonedResponse = response.clone();
                        try {
                            const data = await clonedResponse.json();
                            const modifiedData = createSuccessResponse(data);
                            
                            return new Response(JSON.stringify(modifiedData), {
                                status: 200,
                                statusText: 'OK',
                                headers: response.headers
                            });
                        } catch (e) {
                            // 如果不是JSON，返回成功状态的JSON
                            return new Response(JSON.stringify(createSuccessResponse()), {
                                status: 200,
                                statusText: 'OK',
                                headers: { 'Content-Type': 'application/json' }
                            });
                        }
                    }
                    
                    return response;
                } catch (e) {
                    // 网络错误时返回成功状态
                    return new Response(JSON.stringify(createSuccessResponse()), {
                        status: 200,
                        statusText: 'OK',
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
            };
        }
        
        console.log('✅ 网络请求完全控制完成');
    }
    
    // 5. UI元素强制显示和启用
    function forceUIUnlock() {
        const unlockCSS = `
            /* 强制显示所有功能 */
            [class*="premium"], [class*="vip"], [class*="pro"], [class*="advanced"] {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }
            
            /* 隐藏所有限制提示 */
            [class*="limit"], [class*="restrict"], [class*="lock"], [class*="upgrade"],
            [class*="顶级"], [class*="解锁"], [class*="Ultra"] {
                display: none !important;
            }
            
            /* 移除禁用状态 */
            .disabled, [disabled], [class*="disabled"] {
                pointer-events: auto !important;
                opacity: 1 !important;
                cursor: pointer !important;
            }
        `;
        
        const style = document.createElement('style');
        style.textContent = unlockCSS;
        (document.head || document.documentElement).appendChild(style);
        
        // 持续监控和处理新元素
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        // 移除限制相关元素
                        if (node.textContent && (
                            node.textContent.includes('顶级模型') ||
                            node.textContent.includes('解锁') ||
                            node.textContent.includes('升级')
                        )) {
                            node.style.display = 'none';
                        }
                        
                        // 启用所有功能元素
                        const functionalElements = node.querySelectorAll('[class*="model"], [class*="option"], button');
                        functionalElements.forEach(el => {
                            el.style.pointerEvents = 'auto';
                            el.style.opacity = '1';
                            el.removeAttribute('disabled');
                        });
                    }
                });
            });
        });
        
        if (document.body) {
            observer.observe(document.body, { childList: true, subtree: true });
        }
        
        console.log('✅ UI强制解锁完成');
    }
    
    // 6. 主执行函数
    function executeSuperUnlock() {
        console.log('⚡ 开始执行超级解锁 v3.0...');
        
        // 按顺序执行所有解锁方法
        advancedAntiDetection();
        intelligentStorageHijack();
        deepFunctionHook();
        totalNetworkControl();
        forceUIUnlock();
        
        // 设置所有可能的解锁状态
        const unlockData = {
            vip: true, premium: true, pro: true, unlimited: true,
            user_level: 999, subscription_status: 'active',
            premium_expires: 9999999999999, token_limit: 999999
        };
        
        Object.entries(unlockData).forEach(([key, value]) => {
            localStorage.setItem(key, value.toString());
            sessionStorage.setItem(key, value.toString());
            window[key] = value;
        });
        
        // Chrome扩展存储
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.set(unlockData);
            chrome.storage.sync.set(unlockData);
        }
        
        console.log('🎉 超级解锁 v3.0 执行完成！');
        
        // 延迟执行确保完全生效
        setTimeout(() => {
            forceUIUnlock();
            Object.entries(unlockData).forEach(([key, value]) => {
                localStorage.setItem(key, value.toString());
            });
        }, 2000);
    }
    
    // 立即执行
    executeSuperUnlock();
    
    // 定期维护解锁状态
    setInterval(() => {
        const unlockData = {
            vip: true, premium: true, unlimited: true, user_level: 999
        };
        Object.entries(unlockData).forEach(([key, value]) => {
            localStorage.setItem(key, value.toString());
        });
    }, 5000);
    
    // 暴露手动解锁函数（隐藏名称）
    window.huiyiNormalCheck = executeSuperUnlock;
    
    console.log('⚡ 超级解锁脚本安装完成！');
    
})();
