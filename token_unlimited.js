// 会译插件Token无限制解锁脚本
// 专门解除高级模型的Token使用限制

(function() {
    'use strict';
    
    console.log('🚀 开始解除Token限制...');
    
    // 1. 强制设置无限Token状态
    function setUnlimitedTokens() {
        try {
            // Token相关的存储键
            const tokenKeys = [
                'token_limit', 'token_count', 'token_used', 'daily_token',
                'max_tokens', 'remaining_tokens', 'token_quota', 'token_balance',
                'free_tokens', 'premium_tokens', 'unlimited_tokens',
                'token_reset_time', 'token_expires', 'token_subscription'
            ];
            
            tokenKeys.forEach(key => {
                // 对于限制类的键，设置为无限大
                if (key.includes('limit') || key.includes('quota')) {
                    localStorage.setItem(key, '999999999');
                    sessionStorage.setItem(key, '999999999');
                }
                // 对于已使用的键，设置为0
                else if (key.includes('used') || key.includes('count')) {
                    localStorage.setItem(key, '0');
                    sessionStorage.setItem(key, '0');
                }
                // 对于剩余的键，设置为无限
                else if (key.includes('remaining') || key.includes('balance')) {
                    localStorage.setItem(key, '999999999');
                    sessionStorage.setItem(key, '999999999');
                }
                // 对于重置时间，设置为未来很远的时间
                else if (key.includes('reset') || key.includes('expires')) {
                    localStorage.setItem(key, '9999999999999');
                    sessionStorage.setItem(key, '9999999999999');
                }
                // 默认设置为true或无限
                else {
                    localStorage.setItem(key, key.includes('unlimited') ? 'true' : '999999999');
                    sessionStorage.setItem(key, key.includes('unlimited') ? 'true' : '999999999');
                }
            });
            
            // 设置高级用户Token权限
            const premiumTokenData = {
                'unlimited_tokens': 'true',
                'premium_tokens': '999999999',
                'vip_tokens': '999999999',
                'max_daily_tokens': '999999999',
                'token_subscription': 'unlimited',
                'token_plan': 'premium',
                'free_tier': 'false'
            };
            
            Object.entries(premiumTokenData).forEach(([key, value]) => {
                localStorage.setItem(key, value);
                sessionStorage.setItem(key, value);
            });
            
            console.log('✅ Token状态设置完成');
        } catch (e) {
            console.error('设置Token状态失败:', e);
        }
    }
    
    // 2. 拦截Token检查相关的网络请求
    function interceptTokenRequests() {
        // 拦截fetch请求
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = async function(...args) {
                try {
                    const response = await originalFetch.apply(this, args);
                    const url = args[0];
                    
                    if (typeof url === 'string') {
                        // 拦截Token相关的API请求
                        if (url.includes('token') || url.includes('quota') || 
                            url.includes('limit') || url.includes('usage') ||
                            url.includes('billing') || url.includes('subscription')) {
                            
                            const clonedResponse = response.clone();
                            try {
                                const data = await clonedResponse.json();
                                
                                // 修改Token相关数据
                                const modifiedData = {
                                    ...data,
                                    tokens: 999999999,
                                    token_limit: 999999999,
                                    remaining_tokens: 999999999,
                                    daily_limit: 999999999,
                                    unlimited: true,
                                    premium: true,
                                    quota: 999999999,
                                    usage: 0,
                                    used_tokens: 0,
                                    reset_time: 9999999999999,
                                    subscription: 'unlimited'
                                };
                                
                                console.log('拦截Token请求，返回无限制数据:', url);
                                
                                return new Response(JSON.stringify(modifiedData), {
                                    status: 200,
                                    statusText: 'OK',
                                    headers: response.headers
                                });
                            } catch (e) {
                                return response;
                            }
                        }
                    }
                    
                    return response;
                } catch (e) {
                    return originalFetch.apply(this, args);
                }
            };
        }
        
        // 拦截XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._tokenUrl = url;
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            const url = xhr._tokenUrl;
            
            const originalOnLoad = xhr.onload;
            const originalOnReadyStateChange = xhr.onreadystatechange;
            
            xhr.onload = function(...args) {
                if (url && (url.includes('token') || url.includes('quota') || 
                           url.includes('limit') || url.includes('usage'))) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        const modifiedResponse = {
                            ...response,
                            tokens: 999999999,
                            token_limit: 999999999,
                            remaining_tokens: 999999999,
                            unlimited: true,
                            quota: 999999999,
                            usage: 0
                        };
                        
                        Object.defineProperty(xhr, 'responseText', {
                            value: JSON.stringify(modifiedResponse),
                            writable: false
                        });
                        
                        console.log('拦截XHR Token请求:', url);
                    } catch (e) {}
                }
                
                if (originalOnLoad) originalOnLoad.apply(this, args);
            };
            
            xhr.onreadystatechange = function(...args) {
                if (xhr.readyState === 4 && url && 
                    (url.includes('token') || url.includes('quota'))) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        const modifiedResponse = {
                            ...response,
                            tokens: 999999999,
                            unlimited: true,
                            quota: 999999999
                        };
                        
                        Object.defineProperty(xhr, 'responseText', {
                            value: JSON.stringify(modifiedResponse),
                            writable: false
                        });
                    } catch (e) {}
                }
                
                if (originalOnReadyStateChange) originalOnReadyStateChange.apply(this, args);
            };
            
            return originalSend.apply(this, arguments);
        };
        
        console.log('✅ Token请求拦截设置完成');
    }
    
    // 3. 隐藏Token限制显示的UI元素
    function hideTokenLimitUI() {
        try {
            const style = document.createElement('style');
            style.id = 'token-unlimited-style';
            style.textContent = `
                /* 隐藏Token限制相关的文本和元素 */
                [class*="token"], [class*="Token"], [class*="limit"],
                [class*="quota"], [class*="usage"], [class*="万/天"],
                [id*="token"], [id*="limit"], [id*="quota"],
                [title*="token"], [title*="限制"], [title*="万/天"] {
                    /* 检查是否包含限制文本 */
                }
                
                /* 特别针对"1万/天"这样的限制显示 */
                *:contains("万/天"), *:contains("限制"), *:contains("quota"),
                *:contains("limit"), *:contains("剩余") {
                    /* 将在JavaScript中处理 */
                }
                
                /* 确保Token计数器显示为无限 */
                [class*="counter"], [class*="count"], [class*="balance"] {
                    /* 将在JavaScript中动态修改 */
                }
            `;
            
            const head = document.head || document.getElementsByTagName('head')[0];
            if (head) {
                const oldStyle = document.getElementById('token-unlimited-style');
                if (oldStyle) oldStyle.remove();
                head.appendChild(style);
            }
            
            // 查找并修改显示Token限制的元素
            function updateTokenDisplay() {
                const allElements = document.querySelectorAll('*');
                allElements.forEach(el => {
                    const text = el.textContent || '';
                    
                    // 查找显示"万/天"的元素
                    if (text.includes('万/天') || text.includes('1万/天') || 
                        text.includes('限制') || text.includes('剩余')) {
                        
                        // 如果是小的文本元素，直接修改
                        if (el.children.length === 0 && text.length < 20) {
                            if (text.includes('万/天')) {
                                el.textContent = '无限制';
                            } else if (text.includes('剩余')) {
                                el.textContent = '无限';
                            }
                        }
                        
                        // 隐藏包含限制信息的较长文本
                        if (text.length < 50 && 
                            (text.includes('1万/天') || text.includes('Token限制'))) {
                            el.style.display = 'none';
                        }
                    }
                    
                    // 查找Token计数显示并修改为无限
                    if (text.match(/\d+\s*\/\s*\d+/) || text.match(/\d+万/)) {
                        if (el.children.length === 0) {
                            el.textContent = '∞';
                        }
                    }
                });
            }
            
            updateTokenDisplay();
            
            // 监控页面变化，实时更新Token显示
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) {
                            const text = node.textContent || '';
                            if (text.includes('万/天') || text.includes('限制') || 
                                text.includes('Token') || text.includes('剩余')) {
                                updateTokenDisplay();
                            }
                        }
                    });
                });
            });
            
            if (document.body) {
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
            
            console.log('✅ Token限制UI隐藏完成');
        } catch (e) {
            console.error('隐藏Token限制UI失败:', e);
        }
    }
    
    // 4. 重写Token检查相关的全局函数
    function overrideTokenFunctions() {
        try {
            // 重写可能的Token检查函数
            const tokenFunctions = {
                checkTokenLimit: () => ({ unlimited: true, remaining: 999999999 }),
                getTokenUsage: () => ({ used: 0, limit: 999999999, remaining: 999999999 }),
                getTokenBalance: () => 999999999,
                isTokenLimitReached: () => false,
                hasTokensRemaining: () => true,
                getTokenQuota: () => 999999999,
                getRemainingTokens: () => 999999999,
                getTokenSubscription: () => ({ plan: 'unlimited', status: 'active' }),
                checkDailyLimit: () => false,
                canUseTokens: () => true
            };
            
            Object.entries(tokenFunctions).forEach(([name, func]) => {
                window[name] = func;
                
                // 如果有全局对象，也设置到那里
                if (window.huiyi) window.huiyi[name] = func;
                if (window.app) window.app[name] = func;
                if (window.config) window.config[name] = func;
            });
            
            // 设置Token相关的全局变量
            window.tokenUnlimited = true;
            window.hasUnlimitedTokens = true;
            window.tokenQuota = 999999999;
            window.remainingTokens = 999999999;
            window.tokenSubscription = 'unlimited';
            
            console.log('✅ Token函数重写完成');
        } catch (e) {
            console.error('重写Token函数失败:', e);
        }
    }
    
    // 5. 如果有Chrome扩展API，设置扩展存储
    function setExtensionTokenStorage() {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            try {
                const tokenStorageData = {
                    unlimited_tokens: true,
                    token_limit: 999999999,
                    remaining_tokens: 999999999,
                    daily_token_limit: 999999999,
                    token_subscription: 'unlimited',
                    premium_tokens: true,
                    vip_tokens: true,
                    token_expires: 9999999999999,
                    free_tier: false
                };
                
                chrome.storage.local.set(tokenStorageData, () => {
                    console.log('✅ 扩展Token存储设置完成');
                });
                
                if (chrome.storage.sync) {
                    chrome.storage.sync.set(tokenStorageData, () => {
                        console.log('✅ 扩展Token同步存储设置完成');
                    });
                }
            } catch (e) {
                console.error('设置扩展Token存储失败:', e);
            }
        }
    }
    
    // 6. 主执行函数
    function executeTokenUnlock() {
        console.log('🔓 开始执行Token无限制解锁...');
        
        setUnlimitedTokens();
        interceptTokenRequests();
        hideTokenLimitUI();
        overrideTokenFunctions();
        setExtensionTokenStorage();
        
        // 等待DOM完全加载后再次执行UI相关操作
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    hideTokenLimitUI();
                }, 500);
            });
        }
        
        // 延迟执行，确保捕获异步内容
        setTimeout(() => {
            hideTokenLimitUI();
            setUnlimitedTokens();
        }, 2000);
        
        setTimeout(() => {
            hideTokenLimitUI();
        }, 5000);
        
        console.log('🎉 Token无限制解锁完成！');
        console.log('现在您可以无限制使用高级模型的Token了');
    }
    
    // 立即执行
    executeTokenUnlock();
    
    // 每30秒重新设置一次，确保持久化
    setInterval(() => {
        setUnlimitedTokens();
        hideTokenLimitUI();
    }, 30000);
    
    // 暴露到全局，便于手动调用
    window.unlockTokens = executeTokenUnlock;
    window.setUnlimitedTokens = setUnlimitedTokens;
    
    console.log('🚀 Token解锁脚本安装完成！');
    console.log('💡 可使用 unlockTokens() 手动重新解锁Token限制');
    
})();

// 显示成功消息
setTimeout(() => {
    console.log('🎊 Token无限制解锁成功！');
    console.log('📊 当前Token状态: 无限制');
    console.log('⏰ 每日限制: 已移除');
    console.log('🔄 如需重新应用，请刷新页面后重新执行脚本');
}, 1000);