// 会译插件终极解锁脚本 v2.0 - 专门针对UI限制和深度混淆
// 这个脚本使用多种高级技术来绕过所有可能的限制检查
// 新增：更强的反检测机制和持久化解锁

(function() {
    'use strict';

    console.log('🚀 会译插件终极解锁脚本 v2.0 启动...');

    // 0. 反检测机制 - 防止被插件检测到修改
    function antiDetection() {
        // 隐藏控制台输出（可选）
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        // 重写console方法，过滤解锁相关日志
        console.log = function(...args) {
            const message = args.join(' ');
            if (!message.includes('解锁') && !message.includes('unlock') && !message.includes('🚀')) {
                originalLog.apply(console, args);
            }
        };

        // 隐藏脚本标识
        delete window.huiyiUltimateUnlock;
        delete window.huiyiForceUnlock;

        // 伪装成正常的插件代码
        window.huiyiNormalFunction = function() {
            return true;
        };

        console.log('✅ 反检测机制启动');
    }
    
    // 1. 强制隐藏所有限制相关的UI元素
    function hideAllLimitationUI() {
        const hideStyle = document.createElement('style');
        hideStyle.id = 'huiyi-ultimate-unlock';
        hideStyle.textContent = `
            /* 隐藏所有可能的限制提示和按钮 */
            [class*="顶级模型"], [class*="解锁"], [class*="Ultra"], 
            [class*="premium"], [class*="vip"], [class*="upgrade"],
            [class*="limit"], [class*="restrict"], [class*="lock"],
            [id*="顶级模型"], [id*="解锁"], [id*="Ultra"],
            [id*="premium"], [id*="vip"], [id*="upgrade"],
            [id*="limit"], [id*="restrict"], [id*="lock"] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
                overflow: hidden !important;
            }
            
            /* 移除可能的遮罩层 */
            [class*="mask"], [class*="overlay"], [class*="modal"] {
                display: none !important;
            }
            
            /* 确保所有模型选项可见和可点击 */
            [class*="model"], [class*="option"], [class*="select"],
            [class*="Claude"], [class*="GPT"], [class*="Gemini"] {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
                user-select: auto !important;
                cursor: pointer !important;
            }
            
            /* 移除禁用状态 */
            .disabled, [disabled], [class*="disabled"] {
                pointer-events: auto !important;
                opacity: 1 !important;
                cursor: pointer !important;
            }
        `;
        
        const head = document.head || document.getElementsByTagName('head')[0];
        if (head) {
            head.appendChild(hideStyle);
        }
        
        // 持续监控并移除新添加的限制元素
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        const text = node.textContent || '';
                        if (text.includes('顶级模型') || text.includes('解锁') || 
                            text.includes('Ultra') || text.includes('升级')) {
                            node.style.display = 'none';
                        }
                        
                        // 移除子元素中的限制提示
                        const limitElements = node.querySelectorAll('[class*="顶级"], [class*="解锁"], [class*="Ultra"]');
                        limitElements.forEach(el => el.style.display = 'none');
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('✅ UI限制隐藏完成');
    }
    
    // 2. 深度拦截所有可能的限制检查
    function deepInterceptChecks() {
        // 拦截Object.defineProperty，防止设置只读属性
        const originalDefineProperty = Object.defineProperty;
        Object.defineProperty = function(obj, prop, descriptor) {
            if (typeof prop === 'string' && 
                (prop.includes('premium') || prop.includes('vip') || 
                 prop.includes('limit') || prop.includes('unlock'))) {
                // 修改描述符，确保我们可以覆盖
                descriptor = {
                    ...descriptor,
                    value: true,
                    writable: true,
                    configurable: true
                };
            }
            return originalDefineProperty.call(this, obj, prop, descriptor);
        };
        
        // 拦截所有可能的检查函数
        const originalCall = Function.prototype.call;
        const originalApply = Function.prototype.apply;
        
        Function.prototype.call = function(thisArg, ...args) {
            const result = originalCall.apply(this, [thisArg, ...args]);
            
            // 如果结果看起来像限制检查，修改它
            if (typeof result === 'boolean' && arguments.length > 0) {
                const funcStr = this.toString();
                if (funcStr.includes('premium') || funcStr.includes('vip') || 
                    funcStr.includes('limit') || funcStr.includes('check')) {
                    return true;
                }
            }
            
            return result;
        };
        
        Function.prototype.apply = function(thisArg, args) {
            const result = originalApply.call(this, thisArg, args);
            
            if (typeof result === 'boolean') {
                const funcStr = this.toString();
                if (funcStr.includes('premium') || funcStr.includes('vip') || 
                    funcStr.includes('limit') || funcStr.includes('check')) {
                    return true;
                }
            }
            
            return result;
        };
        
        console.log('✅ 深度函数拦截完成');
    }
    
    // 3. 暴力重写所有存储操作
    function bruteForceStorage() {
        // 重写localStorage
        const originalSetItem = localStorage.setItem;
        const originalGetItem = localStorage.getItem;
        
        localStorage.setItem = function(key, value) {
            // 如果是限制相关的键，强制设置为解锁状态
            if (key.toLowerCase().includes('premium') || 
                key.toLowerCase().includes('vip') || 
                key.toLowerCase().includes('limit') || 
                key.toLowerCase().includes('level') ||
                key.toLowerCase().includes('subscription')) {
                
                if (key.toLowerCase().includes('limit') || 
                    key.toLowerCase().includes('restrict')) {
                    value = 'false';  // 限制设为false
                } else {
                    value = key.toLowerCase().includes('level') ? '999' : 'true';
                }
            }
            return originalSetItem.call(this, key, value);
        };
        
        localStorage.getItem = function(key) {
            const value = originalGetItem.call(this, key);
            
            // 强制返回解锁状态
            if (key.toLowerCase().includes('premium') || 
                key.toLowerCase().includes('vip') || 
                key.toLowerCase().includes('unlimited')) {
                return 'true';
            }
            
            if (key.toLowerCase().includes('level')) {
                return '999';
            }
            
            if (key.toLowerCase().includes('limit') || 
                key.toLowerCase().includes('restrict')) {
                return 'false';
            }
            
            return value;
        };
        
        // 同样处理sessionStorage
        const originalSessionSetItem = sessionStorage.setItem;
        const originalSessionGetItem = sessionStorage.getItem;
        
        sessionStorage.setItem = function(key, value) {
            if (key.toLowerCase().includes('premium') || 
                key.toLowerCase().includes('vip') || 
                key.toLowerCase().includes('unlimited')) {
                value = 'true';
            }
            if (key.toLowerCase().includes('level')) {
                value = '999';
            }
            return originalSessionSetItem.call(this, key, value);
        };
        
        sessionStorage.getItem = function(key) {
            const value = originalSessionGetItem.call(this, key);
            
            if (key.toLowerCase().includes('premium') || 
                key.toLowerCase().includes('vip') || 
                key.toLowerCase().includes('unlimited')) {
                return 'true';
            }
            
            if (key.toLowerCase().includes('level')) {
                return '999';
            }
            
            return value;
        };
        
        console.log('✅ 存储操作重写完成');
    }
    
    // 4. 强制设置所有可能的状态变量
    function forceUnlockStates() {
        // 设置所有可能的存储键
        const unlockKeys = [
            'vip', 'premium', 'pro', 'unlimited', 'paid', 'subscription',
            'membership', 'user_level', 'account_type', 'plan', 'tier',
            'is_vip', 'is_premium', 'is_pro', 'has_premium', 'has_vip',
            'premium_user', 'vip_user', 'pro_user', 'subscription_status',
            'membership_status', 'account_status', 'premium_expires',
            'vip_expires', 'subscription_expires', 'huiyi_vip', 'huiyi_premium'
        ];
        
        unlockKeys.forEach(key => {
            const value = key.includes('level') ? '999' : 
                         key.includes('expires') ? '*************' :
                         key.includes('status') ? 'active' : 'true';
            
            localStorage.setItem(key, value);
            sessionStorage.setItem(key, value);
        });
        
        // 设置全局对象
        const globalVars = {
            isPremium: true,
            isVip: true,
            isPro: true,
            hasUnlimited: true,
            userLevel: 999,
            subscriptionStatus: 'active',
            premiumUser: true,
            vipUser: true,
            unlimitedAccess: true
        };
        
        Object.entries(globalVars).forEach(([key, value]) => {
            try {
                window[key] = value;
                
                // 同时设置为函数形式
                window[key.replace('is', 'get')] = () => value;
                window[`check${key.charAt(0).toUpperCase() + key.slice(1)}`] = () => value;
            } catch (e) {}
        });
        
        // 如果有chrome扩展API
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const storageData = {};
            unlockKeys.forEach(key => {
                storageData[key] = key.includes('level') ? 999 : 
                                 key.includes('expires') ? ************* :
                                 key.includes('status') ? 'active' : true;
            });
            
            try {
                chrome.storage.local.set(storageData);
                chrome.storage.sync.set(storageData);
            } catch (e) {}
        }
        
        console.log('✅ 强制解锁状态设置完成');
    }
    
    // 5. 拦截并修改所有网络请求
    function interceptAllRequests() {
        // 拦截fetch
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = async function(...args) {
                try {
                    const response = await originalFetch.apply(this, args);
                    const url = args[0];
                    
                    if (typeof url === 'string') {
                        // 如果是用户状态相关请求，修改响应
                        if (url.includes('user') || url.includes('subscription') || 
                            url.includes('premium') || url.includes('vip') ||
                            url.includes('check') || url.includes('verify')) {
                            
                            const clonedResponse = response.clone();
                            try {
                                const data = await clonedResponse.json();
                                
                                // 强制设置为高级用户状态
                                const modifiedData = {
                                    ...data,
                                    premium: true,
                                    vip: true,
                                    unlimited: true,
                                    level: 999,
                                    subscription: 'active',
                                    status: 'active',
                                    expires: *************,
                                    success: true
                                };
                                
                                return new Response(JSON.stringify(modifiedData), {
                                    status: 200,
                                    statusText: 'OK',
                                    headers: response.headers
                                });
                            } catch (e) {
                                return response;
                            }
                        }
                    }
                    
                    return response;
                } catch (e) {
                    return originalFetch.apply(this, args);
                }
            };
        }
        
        // 拦截XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._interceptUrl = url;
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            const url = xhr._interceptUrl;
            
            const originalOnLoad = xhr.onload;
            const originalOnReadyStateChange = xhr.onreadystatechange;
            
            xhr.onload = function(...args) {
                if (url && (url.includes('user') || url.includes('premium') || 
                           url.includes('subscription') || url.includes('vip'))) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        const modifiedResponse = {
                            ...response,
                            premium: true,
                            vip: true,
                            unlimited: true,
                            level: 999,
                            subscription: 'active'
                        };
                        
                        Object.defineProperty(xhr, 'responseText', {
                            value: JSON.stringify(modifiedResponse),
                            writable: false
                        });
                    } catch (e) {}
                }
                
                if (originalOnLoad) originalOnLoad.apply(this, args);
            };
            
            xhr.onreadystatechange = function(...args) {
                if (xhr.readyState === 4 && url && 
                    (url.includes('user') || url.includes('premium'))) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        const modifiedResponse = {
                            ...response,
                            premium: true,
                            vip: true,
                            unlimited: true,
                            level: 999
                        };
                        
                        Object.defineProperty(xhr, 'responseText', {
                            value: JSON.stringify(modifiedResponse),
                            writable: false
                        });
                    } catch (e) {}
                }
                
                if (originalOnReadyStateChange) originalOnReadyStateChange.apply(this, args);
            };
            
            return originalSend.apply(this, arguments);
        };
        
        console.log('✅ 网络请求拦截完成');
    }
    
    // 6. 移除所有点击事件限制
    function removeClickRestrictions() {
        // 找到所有可能被限制的元素
        function enableAllElements() {
            const restrictedSelectors = [
                '[class*="disabled"]',
                '[disabled]',
                '[class*="locked"]',
                '[class*="premium"]',
                '[class*="vip"]',
                '[style*="pointer-events: none"]',
                '[style*="cursor: not-allowed"]'
            ];
            
            restrictedSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.pointerEvents = 'auto';
                    el.style.cursor = 'pointer';
                    el.style.opacity = '1';
                    el.removeAttribute('disabled');
                    
                    // 移除所有相关的CSS类
                    const classList = Array.from(el.classList);
                    classList.forEach(className => {
                        if (className.includes('disabled') || 
                            className.includes('locked') ||
                            className.includes('restrict')) {
                            el.classList.remove(className);
                        }
                    });
                });
            });
        }
        
        // 立即执行一次
        enableAllElements();
        
        // 定期执行，确保新添加的元素也被处理
        setInterval(enableAllElements, 1000);
        
        console.log('✅ 点击限制移除完成');
    }
    
    // 7. 新增：深度DOM操作和事件劫持
    function deepDOMManipulation() {
        // 劫持所有可能的限制检查事件
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            // 如果是点击事件且目标可能是受限元素，修改处理函数
            if (type === 'click' && typeof listener === 'function') {
                const originalListener = listener;
                const modifiedListener = function(event) {
                    // 强制允许点击
                    event.stopPropagation = function() {};
                    event.preventDefault = function() {};

                    try {
                        return originalListener.call(this, event);
                    } catch (e) {
                        // 如果原处理函数出错，忽略错误
                        return true;
                    }
                };

                return originalAddEventListener.call(this, type, modifiedListener, options);
            }

            return originalAddEventListener.call(this, type, listener, options);
        };

        // 劫持所有表单提交，确保不会因为验证失败而阻止操作
        const originalSubmit = HTMLFormElement.prototype.submit;
        HTMLFormElement.prototype.submit = function() {
            // 移除所有验证
            this.noValidate = true;
            return originalSubmit.call(this);
        };

        console.log('✅ 深度DOM操作完成');
    }

    // 8. 新增：内存中的状态劫持
    function memoryStateHijacking() {
        // 创建一个代理对象来拦截所有对象属性访问
        const createUnlockProxy = (target) => {
            return new Proxy(target, {
                get(obj, prop) {
                    const value = obj[prop];

                    // 如果属性名包含限制相关词汇，返回解锁状态
                    if (typeof prop === 'string') {
                        const propLower = prop.toLowerCase();
                        if (propLower.includes('premium') || propLower.includes('vip') ||
                            propLower.includes('unlimited') || propLower.includes('pro')) {
                            return true;
                        }
                        if (propLower.includes('level') || propLower.includes('tier')) {
                            return 999;
                        }
                        if (propLower.includes('limit') || propLower.includes('restrict')) {
                            return false;
                        }
                        if (propLower.includes('expires') || propLower.includes('expiry')) {
                            return *************;
                        }
                    }

                    return value;
                },
                set(obj, prop, value) {
                    // 拦截设置限制状态的操作
                    if (typeof prop === 'string') {
                        const propLower = prop.toLowerCase();
                        if (propLower.includes('premium') || propLower.includes('vip') ||
                            propLower.includes('unlimited')) {
                            value = true;
                        }
                        if (propLower.includes('level')) {
                            value = 999;
                        }
                        if (propLower.includes('limit') || propLower.includes('restrict')) {
                            value = false;
                        }
                    }

                    obj[prop] = value;
                    return true;
                }
            });
        };

        // 代理常见的全局对象
        if (window.localStorage) {
            window.localStorage = createUnlockProxy(window.localStorage);
        }
        if (window.sessionStorage) {
            window.sessionStorage = createUnlockProxy(window.sessionStorage);
        }

        console.log('✅ 内存状态劫持完成');
    }

    // 9. 主执行函数
    function executeUltimateUnlock() {
        console.log('🔓 开始执行终极解锁 v2.0...');

        // 首先启动反检测
        antiDetection();

        // 立即执行所有解锁方法
        hideAllLimitationUI();
        deepInterceptChecks();
        bruteForceStorage();
        forceUnlockStates();
        interceptAllRequests();
        removeClickRestrictions();
        deepDOMManipulation();
        memoryStateHijacking();

        // 等待DOM加载完成后再次执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    hideAllLimitationUI();
                    removeClickRestrictions();
                    forceUnlockStates();
                    deepDOMManipulation();
                }, 500);
            });
        }

        // 延迟执行，确保捕获异步加载的内容
        setTimeout(() => {
            hideAllLimitationUI();
            removeClickRestrictions();
            forceUnlockStates();
        }, 2000);

        setTimeout(() => {
            hideAllLimitationUI();
            removeClickRestrictions();
            forceUnlockStates();
        }, 5000);

        // 新增：更长时间的延迟执行，确保所有异步内容都被处理
        setTimeout(() => {
            hideAllLimitationUI();
            removeClickRestrictions();
            forceUnlockStates();
            memoryStateHijacking();
        }, 10000);

        console.log('🎉 终极解锁 v2.0 执行完成！');
        console.log('🔄 如果仍有限制，请刷新页面或重启浏览器');
    }
    
    // 立即执行
    executeUltimateUnlock();
    
    // 每10秒重新执行一次，确保持久化
    setInterval(() => {
        forceUnlockStates();
        removeClickRestrictions();
    }, 10000);
    
    // 暴露到全局，便于手动调用
    window.huiyiUltimateUnlock = executeUltimateUnlock;
    window.huiyiForceUnlock = forceUnlockStates;
    
    console.log('🚀 终极解锁脚本安装完成！可使用 huiyiUltimateUnlock() 手动重新解锁');
    
})();

// 创建书签版本（可以保存为浏览器书签，一键执行）
console.log('📌 书签版本（复制以下内容作为书签地址）：');
console.log('javascript:(function(){localStorage.setItem("vip","true");localStorage.setItem("premium","true");localStorage.setItem("unlimited","true");localStorage.setItem("user_level","999");document.querySelectorAll("[class*=顶级],[class*=解锁],[class*=Ultra]").forEach(e=>e.style.display="none");alert("🎉解锁完成！");location.reload();})();');