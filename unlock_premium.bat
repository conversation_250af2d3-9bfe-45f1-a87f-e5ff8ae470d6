@echo off
REM 会译插件顶级模型解锁批处理脚本
echo 正在解锁会译插件的顶级模型限制...

REM 1. 备份原始文件
echo 正在备份原始文件...
if not exist "backup" mkdir backup
copy background.js backup\background.js.bak 2>nul
copy content.js backup\content.js.bak 2>nul
copy popup.js backup\popup.js.bak 2>nul
copy sidePanel.js backup\sidePanel.js.bak 2>nul
copy option.js backup\option.js.bak 2>nul

REM 2. 清理浏览器存储中的限制信息
echo 正在清理浏览器存储...
powershell -Command "& {
    # 清理Chrome扩展存储
    $chromeUserData = $env:LOCALAPPDATA + '\Google\Chrome\User Data\Default\Local Extension Settings'
    $edgeUserData = $env:LOCALAPPDATA + '\Microsoft\Edge\User Data\Default\Local Extension Settings'
    
    # 查找会译插件的存储目录
    if (Test-Path $chromeUserData) {
        Get-ChildItem $chromeUserData | ForEach-Object {
            $manifestPath = Join-Path $_.FullName 'manifest.json'
            if (Test-Path $manifestPath) {
                $content = Get-Content $manifestPath -Raw -ErrorAction SilentlyContinue
                if ($content -like '*会译*' -or $content -like '*huiyi*') {
                    Write-Host '找到Chrome中的会译插件存储，正在清理...'
                    Remove-Item $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
                }
            }
        }
    }
    
    if (Test-Path $edgeUserData) {
        Get-ChildItem $edgeUserData | ForEach-Object {
            $manifestPath = Join-Path $_.FullName 'manifest.json'
            if (Test-Path $manifestPath) {
                $content = Get-Content $manifestPath -Raw -ErrorAction SilentlyContinue
                if ($content -like '*会译*' -or $content -like '*huiyi*') {
                    Write-Host '找到Edge中的会译插件存储，正在清理...'
                    Remove-Item $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
                }
            }
        }
    }
}"

REM 3. 修改manifest.json以添加更多权限
echo 正在修改插件权限...
powershell -Command "& {
    $manifestPath = 'manifest.json'
    if (Test-Path $manifestPath) {
        $manifest = Get-Content $manifestPath -Raw | ConvertFrom-Json
        
        # 添加必要的权限
        $permissions = @('storage', 'unlimitedStorage', 'scripting', 'proxy', 'tts', 'contextMenus', 'webRequest', 'sidePanel', 'activeTab', 'tabs')
        $manifest.permissions = $permissions
        
        # 添加主机权限
        $manifest.host_permissions = @('<all_urls>')
        
        # 保存修改后的manifest
        $manifest | ConvertTo-Json -Depth 10 | Set-Content $manifestPath -Encoding UTF8
        Write-Host 'manifest.json 权限修改完成'
    }
}"

REM 4. 注入解锁代码到主要的JS文件
echo 正在注入解锁代码...

REM 创建解锁代码片段
echo // 会译插件解锁代码 > unlock_snippet.js
echo (function() { >> unlock_snippet.js
echo     'use strict'; >> unlock_snippet.js
echo     console.log('会译插件解锁代码已注入'); >> unlock_snippet.js
echo     // 设置本地存储为高级用户 >> unlock_snippet.js
echo     try { >> unlock_snippet.js
echo         localStorage.setItem('vip', 'true'); >> unlock_snippet.js
echo         localStorage.setItem('premium', 'true'); >> unlock_snippet.js
echo         localStorage.setItem('unlimited', 'true'); >> unlock_snippet.js
echo         localStorage.setItem('user_level', '999'); >> unlock_snippet.js
echo         localStorage.setItem('subscription_status', 'active'); >> unlock_snippet.js
echo         localStorage.setItem('premium_expires', '9999999999999'); >> unlock_snippet.js
echo     } catch(e) { console.error('解锁失败:', e); } >> unlock_snippet.js
echo     // 覆盖可能的检查函数 >> unlock_snippet.js
echo     window.isPremium = function() { return true; }; >> unlock_snippet.js
echo     window.isVip = function() { return true; }; >> unlock_snippet.js
echo     window.hasUnlimited = function() { return true; }; >> unlock_snippet.js
echo     window.checkSubscription = function() { return {status: 'active', level: 999}; }; >> unlock_snippet.js
echo })(); >> unlock_snippet.js

REM 将解锁代码添加到主要文件的开头
echo 正在修改background.js...
type unlock_snippet.js > temp_background.js
type background.js >> temp_background.js
move temp_background.js background.js

echo 正在修改content.js...
type unlock_snippet.js > temp_content.js
type content.js >> temp_content.js
move temp_content.js content.js

echo 正在修改popup.js...
type unlock_snippet.js > temp_popup.js
type popup.js >> temp_popup.js
move temp_popup.js popup.js

REM 清理临时文件
del unlock_snippet.js

REM 5. 创建持久化脚本
echo 正在创建持久化脚本...
echo // 会译插件持久化解锁脚本 > persistent_unlock.js
echo setInterval(function() { >> persistent_unlock.js
echo     try { >> persistent_unlock.js
echo         localStorage.setItem('vip', 'true'); >> persistent_unlock.js
echo         localStorage.setItem('premium', 'true'); >> persistent_unlock.js
echo         localStorage.setItem('unlimited', 'true'); >> persistent_unlock.js
echo         localStorage.setItem('user_level', '999'); >> persistent_unlock.js
echo         if (typeof chrome !== 'undefined' ^&^& chrome.storage) { >> persistent_unlock.js
echo             chrome.storage.local.set({vip: true, premium: true, unlimited: true}); >> persistent_unlock.js
echo         } >> persistent_unlock.js
echo     } catch(e) {} >> persistent_unlock.js
echo }, 1000); >> persistent_unlock.js

echo.
echo ================================
echo 会译插件顶级模型解锁完成！
echo ================================
echo.
echo 操作说明：
echo 1. 所有原始文件已备份到 backup 目录
echo 2. 插件权限已扩展
echo 3. 解锁代码已注入到主要文件
echo 4. 创建了持久化解锁脚本
echo.
echo 下一步操作：
echo 1. 关闭所有浏览器窗口
echo 2. 重新启动浏览器
echo 3. 重新加载会译插件（在扩展管理页面点击重新加载）
echo 4. 测试顶级模型是否可用
echo.
echo 如果需要恢复原始文件，请运行：
echo copy backup\*.bak .
echo.
pause