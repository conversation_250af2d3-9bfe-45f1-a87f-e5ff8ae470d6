// 会译插件顶级模型解锁脚本
// 该脚本尝试通过修改本地存储和运行时配置来解锁顶级模型

(function() {
    'use strict';
    
    console.log('开始执行会译插件顶级模型解锁...');
    
    // 方法1: 修改本地存储
    function modifyLocalStorage() {
        try {
            // 常见的会员状态标识
            const premiumKeys = [
                'vip', 'premium', 'pro', 'paid', 'subscription',
                'membership', 'user_level', 'account_type', 'plan',
                'unlimited', 'level', 'tier', 'status',
                // 新增Token相关键
                'token_limit', 'token_quota', 'daily_tokens', 'max_tokens',
                'unlimited_tokens', 'premium_tokens', 'token_subscription'
            ];
            
            // 尝试设置会员状态
            premiumKeys.forEach(key => {
                localStorage.setItem(key, 'true');
                localStorage.setItem(key.toUpperCase(), 'true');
                localStorage.setItem(`is_${key}`, 'true');
                localStorage.setItem(`has_${key}`, 'true');
                localStorage.setItem(`${key}_user`, 'true');
                localStorage.setItem(`${key}_status`, 'active');
                localStorage.setItem(`${key}_level`, '999');
            });
            
            // 设置高级会员等级
            localStorage.setItem('user_level', '999');
            localStorage.setItem('membership_level', '999');
            localStorage.setItem('account_level', 'premium');
            localStorage.setItem('subscription_status', 'active');
            localStorage.setItem('premium_expires', '*************');
            localStorage.setItem('vip_expires', '*************');
            
            // 新增：设置Token无限制
            localStorage.setItem('token_limit', '*********');
            localStorage.setItem('token_quota', '*********');
            localStorage.setItem('daily_tokens', '*********');
            localStorage.setItem('max_tokens', '*********');
            localStorage.setItem('remaining_tokens', '*********');
            localStorage.setItem('unlimited_tokens', 'true');
            localStorage.setItem('premium_tokens', 'true');
            localStorage.setItem('token_subscription', 'unlimited');
            localStorage.setItem('token_used', '0');
            localStorage.setItem('token_reset_time', '*************');
            
            console.log('本地存储修改完成');
        } catch (e) {
            console.error('修改本地存储失败:', e);
        }
    }
    
    // 方法2: 修改扩展存储
    function modifyExtensionStorage() {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            try {
                const premiumData = {
                    'vip': true,
                    'premium': true,
                    'pro': true,
                    'paid': true,
                    'unlimited': true,
                    'subscription': 'active',
                    'membership': 'premium',
                    'user_level': 999,
                    'account_type': 'premium',
                    'plan': 'premium',
                    'tier': 'premium',
                    'premium_user': true,
                    'vip_user': true,
                    'pro_user': true,
                    'is_premium': true,
                    'is_vip': true,
                    'is_pro': true,
                    'has_premium': true,
                    'premium_status': 'active',
                    'vip_status': 'active',
                    'premium_expires': *************,
                    'vip_expires': *************,
                    'subscription_expires': *************
                };
                
                // 同时设置到 local 和 sync 存储
                chrome.storage.local.set(premiumData, () => {
                    console.log('扩展本地存储设置完成');
                });
                
                chrome.storage.sync.set(premiumData, () => {
                    console.log('扩展同步存储设置完成');
                });
                
            } catch (e) {
                console.error('修改扩展存储失败:', e);
            }
        }
    }
    
    // 方法3: 拦截和修改网络请求
    function interceptRequests() {
        if (typeof chrome !== 'undefined' && chrome.webRequest) {
            try {
                // 拦截用户信息请求，返回高级用户信息
                chrome.webRequest.onBeforeRequest.addListener(
                    function(details) {
                        if (details.url.includes('user') || details.url.includes('profile') || 
                            details.url.includes('membership') || details.url.includes('subscription')) {
                            console.log('拦截到用户信息请求:', details.url);
                        }
                    },
                    {urls: ["<all_urls>"]},
                    ["requestBody"]
                );
                
                // 修改响应头
                chrome.webRequest.onHeadersReceived.addListener(
                    function(details) {
                        if (details.url.includes('api')) {
                            console.log('拦截到API响应:', details.url);
                        }
                    },
                    {urls: ["<all_urls>"]},
                    ["responseHeaders"]
                );
                
            } catch (e) {
                console.error('网络请求拦截设置失败:', e);
            }
        }
    }
    
    // 方法4: 尝试修改全局对象
    function modifyGlobalObjects() {
        try {
            // 创建全局会员状态对象
            window.isPremium = true;
            window.isVip = true;
            window.isPro = true;
            window.hasUnlimited = true;
            window.userLevel = 999;
            window.membershipLevel = 999;
            window.subscriptionStatus = 'active';
            
            // 如果存在配置对象，尝试修改
            if (window.config) {
                window.config.premium = true;
                window.config.vip = true;
                window.config.unlimited = true;
            }
            
            // 创建配置对象
            window.huiyiConfig = {
                premium: true,
                vip: true,
                pro: true,
                unlimited: true,
                userLevel: 999,
                subscription: 'active'
            };
            
            console.log('全局对象修改完成');
        } catch (e) {
            console.error('修改全局对象失败:', e);
        }
    }
    
    // 方法5: 监听并修改消息传递
    function interceptMessages() {
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            try {
                // 监听扩展内部消息
                chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                    console.log('拦截到消息:', message);
                    
                    // 如果是查询用户状态的消息，返回高级用户状态
                    if (message && (message.type === 'getUserStatus' || 
                                  message.action === 'checkMembership' ||
                                  message.cmd === 'getUserInfo')) {
                        sendResponse({
                            success: true,
                            premium: true,
                            vip: true,
                            unlimited: true,
                            userLevel: 999,
                            subscription: 'active',
                            expires: *************
                        });
                        return true;
                    }
                });
                
                console.log('消息拦截设置完成');
            } catch (e) {
                console.error('消息拦截设置失败:', e);
            }
        }
    }
    
    // 新增：强制隐藏UI限制元素
    function hideUIRestrictions() {
        try {
            // 创建强制隐藏样式
            const hideStyle = document.createElement('style');
            hideStyle.id = 'huiyi-unlock-style';
            hideStyle.textContent = `
                /* 隐藏所有限制相关的UI元素 */
                [class*="顶级模型"], [class*="解锁"], [class*="Ultra"],
                [id*="顶级模型"], [id*="解锁"], [id*="Ultra"],
                [data-text*="顶级模型"], [data-text*="解锁"],
                [title*="解锁"], [title*="顶级"] {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                }
                
                /* 确保所有模型选项可见 */
                [class*="model"], [class*="Claude"], [class*="GPT"], [class*="Gemini"] {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    pointer-events: auto !important;
                }
                
                /* 移除禁用状态 */
                .disabled, [disabled] {
                    pointer-events: auto !important;
                    opacity: 1 !important;
                    cursor: pointer !important;
                }
            `;
            
            const head = document.head || document.getElementsByTagName('head')[0];
            if (head) {
                // 移除旧样式
                const oldStyle = document.getElementById('huiyi-unlock-style');
                if (oldStyle) oldStyle.remove();
                
                head.appendChild(hideStyle);
            }
            
            // 直接查找并隐藏包含限制文本的元素
            const allElements = document.querySelectorAll('*');
            allElements.forEach(el => {
                const text = el.textContent || '';
                if (text.includes('顶级模型') || text.includes('解锁顶级模型') || text.includes('Ultra')) {
                    el.style.display = 'none';
                }
            });
            
            console.log('UI限制隐藏完成');
        } catch (e) {
            console.error('隐藏UI限制失败:', e);
        }
    }
    
    // 新增：强制启用所有模型选项
    function enableAllModels() {
        try {
            // 查找所有可能的模型选择器
            const modelSelectors = [
                '[class*="model"]',
                '[class*="option"]', 
                '[class*="select"]',
                '[class*="Claude"]',
                '[class*="GPT"]',
                '[class*="Gemini"]',
                'button',
                '[role="button"]',
                '[role="option"]'
            ];
            
            modelSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    // 移除所有可能的限制
                    el.removeAttribute('disabled');
                    el.style.pointerEvents = 'auto';
                    el.style.cursor = 'pointer';
                    el.style.opacity = '1';
                    
                    // 移除限制相关的CSS类
                    const classList = Array.from(el.classList);
                    classList.forEach(className => {
                        if (className.includes('disabled') || 
                            className.includes('locked') ||
                            className.includes('restrict')) {
                            el.classList.remove(className);
                        }
                    });
                });
            });
            
            console.log('所有模型选项启用完成');
        } catch (e) {
            console.error('启用模型选项失败:', e);
        }
    }
    
    // 执行所有修改方法
    function executeUnlock() {
        console.log('正在执行解锁操作...');
        
        modifyLocalStorage();
        modifyExtensionStorage();
        interceptRequests();
        modifyGlobalObjects();
        interceptMessages();
        hideUIRestrictions();
        enableAllModels();
        
        console.log('解锁操作完成！请重启浏览器以确保更改生效。');
        
        // 延迟执行，确保所有异步操作完成
        setTimeout(() => {
            console.log('延迟检查: 所有修改操作已完成');
            hideUIRestrictions();
            enableAllModels();
        }, 2000);
        
        // 继续监控页面变化
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(() => {
                hideUIRestrictions();
                enableAllModels();
            });
            
            if (document.body) {
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
        }
    }
    
    // 立即执行
    executeUnlock();
    
    // 每5秒重复执行一次，确保持久化
    setInterval(executeUnlock, 5000);
    
})();