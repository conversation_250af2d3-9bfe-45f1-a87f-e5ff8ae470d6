"use strict";(()=>{var I=Object.create;var _=Object.defineProperty;var Z=Object.getOwnPropertyDescriptor;var z=Object.getOwnPropertyNames;var G=Object.getPrototypeOf,H=Object.prototype.hasOwnProperty;var $=(i,m)=>()=>(m||i((m={exports:{}}).exports,m),m.exports);var V=(i,m,c,u)=>{if(m&&typeof m=="object"||typeof m=="function")for(let d of z(m))!H.call(i,d)&&d!==c&&_(i,d,{get:()=>m[d],enumerable:!(u=Z(m,d))||u.enumerable});return i};var h=(i,m,c)=>(c=i!=null?I(G(i)):{},V(m||!i||!i.__esModule?_(c,"default",{value:i,enumerable:!0}):c,i));var w=$(()=>{"use strict";globalThis&&(globalThis.__huiyi_envvars__={host:"https://huiyiai.net",browser:"edge",oem:"default",buildMode:"pack"})});var L=$((E,F)=>{var X=h(w());(function(i,m){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],m);else if(typeof E<"u")m(F);else{var c={exports:{}};m(c),i.browser=c.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:E,function(i){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let m="The message port closed before a response was received.",c=u=>{let d={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(d).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class P extends WeakMap{constructor(r,g=void 0){super(g),this.createItem=r}get(r){return this.has(r)||this.set(r,this.createItem(r)),super.get(r)}}let B=e=>e&&typeof e=="object"&&typeof e.then=="function",S=(e,r)=>(...g)=>{u.runtime.lastError?e.reject(new Error(u.runtime.lastError.message)):r.singleCallbackArg||g.length<=1&&r.singleCallbackArg!==!1?e.resolve(g[0]):e.resolve(g)},p=e=>e==1?"argument":"arguments",W=(e,r)=>function(n,...a){if(a.length<r.minArgs)throw new Error(`Expected at least ${r.minArgs} ${p(r.minArgs)} for ${e}(), got ${a.length}`);if(a.length>r.maxArgs)throw new Error(`Expected at most ${r.maxArgs} ${p(r.maxArgs)} for ${e}(), got ${a.length}`);return new Promise((A,o)=>{if(r.fallbackToNoCallback)try{n[e](...a,S({resolve:A,reject:o},r))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),n[e](...a),r.fallbackToNoCallback=!1,r.noCallback=!0,A()}else r.noCallback?(n[e](...a),A()):n[e](...a,S({resolve:A,reject:o},r))})},M=(e,r,g)=>new Proxy(r,{apply(n,a,A){return g.call(a,e,...A)}}),k=Function.call.bind(Object.prototype.hasOwnProperty),y=(e,r={},g={})=>{let n=Object.create(null),a={has(o,s){return s in e||s in n},get(o,s,l){if(s in n)return n[s];if(!(s in e))return;let t=e[s];if(typeof t=="function")if(typeof r[s]=="function")t=M(e,e[s],r[s]);else if(k(g,s)){let f=W(s,g[s]);t=M(e,e[s],f)}else t=t.bind(e);else if(typeof t=="object"&&t!==null&&(k(r,s)||k(g,s)))t=y(t,r[s],g[s]);else if(k(g,"*"))t=y(t,r[s],g["*"]);else return Object.defineProperty(n,s,{configurable:!0,enumerable:!0,get(){return e[s]},set(f){e[s]=f}}),t;return n[s]=t,t},set(o,s,l,t){return s in n?n[s]=l:e[s]=l,!0},defineProperty(o,s,l){return Reflect.defineProperty(n,s,l)},deleteProperty(o,s){return Reflect.deleteProperty(n,s)}},A=Object.create(e);return new Proxy(A,a)},T=e=>({addListener(r,g,...n){r.addListener(e.get(g),...n)},hasListener(r,g){return r.hasListener(e.get(g))},removeListener(r,g){r.removeListener(e.get(g))}}),q=new P(e=>typeof e!="function"?e:function(g){let n=y(g,{},{getContent:{minArgs:0,maxArgs:0}});e(n)}),R=new P(e=>typeof e!="function"?e:function(g,n,a){let A=!1,o,s=new Promise(b=>{o=function(x){A=!0,b(x)}}),l;try{l=e(g,n,o)}catch(b){l=Promise.reject(b)}let t=l!==!0&&B(l);if(l!==!0&&!t&&!A)return!1;let f=b=>{b.then(x=>{a(x)},x=>{let v;x&&(x instanceof Error||typeof x.message=="string")?v=x.message:v="An unexpected error occurred",a({__mozWebExtensionPolyfillReject__:!0,message:v})}).catch(x=>{console.error("Failed to send onMessage rejected reply",x)})};return f(t?l:s),!0}),U=({reject:e,resolve:r},g)=>{u.runtime.lastError?u.runtime.lastError.message===m?r():e(new Error(u.runtime.lastError.message)):g&&g.__mozWebExtensionPolyfillReject__?e(new Error(g.message)):r(g)},N=(e,r,g,...n)=>{if(n.length<r.minArgs)throw new Error(`Expected at least ${r.minArgs} ${p(r.minArgs)} for ${e}(), got ${n.length}`);if(n.length>r.maxArgs)throw new Error(`Expected at most ${r.maxArgs} ${p(r.maxArgs)} for ${e}(), got ${n.length}`);return new Promise((a,A)=>{let o=U.bind(null,{resolve:a,reject:A});n.push(o),g.sendMessage(...n)})},D={devtools:{network:{onRequestFinished:T(q)}},runtime:{onMessage:T(R),onMessageExternal:T(R),sendMessage:N.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:N.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},C={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return d.privacy={network:{"*":C},services:{"*":C},websites:{"*":C}},y(u,D,d)};i.exports=c(chrome)}else i.exports=globalThis.browser})});var Y=h(w(),1),j=h(L(),1),O=document.createElement("script");O.src=j.default.runtime.getURL("/video.js");document.documentElement.prepend(O);})();
