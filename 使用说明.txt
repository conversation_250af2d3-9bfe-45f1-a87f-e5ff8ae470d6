会译插件完全解锁工具 v4.0 - 使用说明
===========================================

🎯 目标：完全解锁会译AI翻译插件
- 🔐 绕过登录限制（无需注册登录）
- 🚀 解除所有模型使用限制
- 💎 无限次数使用，无Token限制

📋 快速使用步骤：

方法一：完全解锁脚本（最简单，推荐）
1. 关闭所有浏览器窗口
2. 双击运行 "super_unlock.bat"
3. 等待脚本执行完成
4. 重启浏览器
5. 重新加载会译插件
6. 无需登录，直接使用所有功能！

方法二：书签解锁（最便捷）
选择以下任一书签：

A. 完全解锁书签（登录绕过+模型解锁）
1. 打开 "bookmark_unlock.js" 文件
2. 复制 "完全解锁版本" 的 "javascript:" 代码
3. 在浏览器中创建新书签，粘贴代码到URL
4. 在会译插件页面点击书签
5. 完成！无需登录即可使用所有功能

B. 登录绕过专用书签
1. 复制 "登录绕过专用版本" 的代码
2. 创建书签并在插件页面使用
3. 专门解决登录限制问题

方法三：手动解锁（最灵活）
1. 打开会译插件页面
2. 按F12打开开发者工具
3. 切换到Console标签
4. 复制 "complete_unlock.js" 中的代码并执行
5. 完成！

🔍 验证解锁效果：
- ✅ 无需登录即可使用插件
- ✅ 可以使用GPT-4、Claude 3等所有高级模型
- ✅ 没有使用次数限制
- ✅ 没有Token限制
- ✅ 升级提示已消失

⚠️ 注意事项：
- 使用前请备份重要数据
- 仅供学习研究使用
- 遵守相关法律法规

🆘 遇到问题？
1. 查看 "解锁指南.md" 详细说明
2. 尝试不同的解锁方法
3. 清除浏览器缓存后重试

📞 技术支持：
- 详细指南：解锁指南.md
- 项目说明：README.md
- 问题反馈：GitHub Issues

🎉 享受无限制的AI翻译体验！
