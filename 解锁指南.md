# 会译插件超级解锁指南 v3.0

## 概述
本指南提供了多种强大的方法来解锁会译插件的顶级模型限制，让您可以免费使用所有高级AI模型功能。新版本包含了更强的反检测机制和持久化解锁技术。

## 🚀 方法一：超级解锁脚本（最强推荐）

### 步骤 1：运行超级解锁脚本
1. 确保浏览器已完全关闭
2. 双击运行 `super_unlock.bat`
3. 等待脚本自动执行完成（约30秒）
4. 脚本会自动：
   - 备份所有原始文件
   - 清理浏览器存储和缓存
   - 注入超级解锁代码
   - 扩展插件权限
   - 自动打开扩展管理页面

### 步骤 2：重新加载插件
1. 在自动打开的扩展管理页面中
2. 找到会译插件，点击"重新加载"按钮
3. 或者先关闭插件再重新启用
4. 享受无限制的AI模型！

## 📚 方法二：一键书签解锁（最便捷）

### 创建解锁书签
1. 在浏览器中创建新书签
2. 复制 `bookmark_unlock.js` 中的任意一个 `javascript:` 代码
3. 将代码粘贴到书签的URL地址中
4. 保存书签

### 使用解锁书签
1. 打开会译插件页面
2. 点击刚创建的解锁书签
3. 等待解锁完成提示
4. 刷新页面即可使用所有功能

### 书签版本说明
- **标准版本**：适合大多数用户，有成功提示
- **增强版本**：功能最全面，解锁效果最好
- **静默版本**：无弹窗，适合不想被打扰的用户
- **持续监控版本**：自动维护解锁状态，适合长期使用

## 🔧 方法三：手动注入解锁代码

### 步骤 1：注入超级解锁代码
1. 在浏览器中打开开发者工具（F12）
2. 切换到 Console 标签页
3. 复制并粘贴 `super_unlock.js` 中的完整代码
4. 按回车执行

### 步骤 2：验证解锁效果
1. 检查控制台是否显示"⚡ 超级解锁脚本安装完成！"
2. 刷新页面
3. 测试高级模型是否可用

### 可选：注入其他解锁脚本
- `unlock_premium.js`：基础解锁功能
- `advanced_unlock.js`：高级拦截功能
- `ultimate_unlock.js`：终极解锁功能
- `token_unlimited.js`：Token无限制功能

## 方法三：修改插件文件（高级用户）

### 步骤 1：备份原始文件
```bash
mkdir backup
copy background.js backup/
copy content.js backup/
copy popup.js backup/
copy sidePanel.js backup/
copy option.js backup/
```

### 步骤 2：修改 manifest.json
在 permissions 数组中添加：
```json
"permissions": [
    "storage",
    "unlimitedStorage", 
    "scripting",
    "proxy",
    "tts",
    "contextMenus",
    "webRequest",
    "sidePanel",
    "activeTab",
    "tabs"
]
```

### 步骤 3：注入解锁代码
将以下代码添加到每个主要JS文件的开头：

```javascript
// 解锁代码
(function() {
    try {
        localStorage.setItem('vip', 'true');
        localStorage.setItem('premium', 'true');
        localStorage.setItem('unlimited', 'true');
        localStorage.setItem('user_level', '999');
        localStorage.setItem('subscription_status', 'active');
        
        window.isPremium = () => true;
        window.isVip = () => true;
        window.hasUnlimited = () => true;
        
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.set({
                vip: true,
                premium: true,
                unlimited: true,
                user_level: 999
            });
        }
    } catch(e) {
        console.error('解锁失败:', e);
    }
})();
```

## 方法四：浏览器存储修改

### Chrome 浏览器
1. 进入：chrome://settings/content/all
2. 搜索 "huiyiai.net" 或插件相关域名
3. 清除所有存储数据
4. 打开开发者工具，在 Console 中执行：

```javascript
// 设置高级用户状态
localStorage.setItem('vip', 'true');
localStorage.setItem('premium', 'true');
localStorage.setItem('unlimited', 'true');
localStorage.setItem('user_level', '999');
localStorage.setItem('subscription_status', 'active');
localStorage.setItem('premium_expires', '9999999999999');

// 如果有sessionStorage也设置
sessionStorage.setItem('vip', 'true');
sessionStorage.setItem('premium', 'true');
sessionStorage.setItem('unlimited', 'true');
```

### Edge 浏览器
1. 进入：edge://settings/content/all
2. 搜索相关域名并清除存储
3. 执行上述相同的 JavaScript 代码

## 验证解锁是否成功

### 检查存储状态
在开发者工具 Console 中执行：
```javascript
console.log('VIP状态:', localStorage.getItem('vip'));
console.log('Premium状态:', localStorage.getItem('premium'));
console.log('用户等级:', localStorage.getItem('user_level'));
```

### 检查功能可用性
1. 尝试使用之前被限制的高级模型
2. 检查是否还有使用次数限制
3. 查看是否有升级提示消失

## 故障排除

### 问题1：解锁后仍然有限制
**解决方案：**
1. 清除浏览器缓存和 Cookie
2. 重启浏览器
3. 重新运行解锁脚本
4. 检查是否有多个浏览器配置文件

### 问题2：插件无法加载
**解决方案：**
1. 从 backup 文件夹恢复原始文件
2. 检查 manifest.json 语法是否正确
3. 在扩展管理页面查看错误信息

### 问题3：解锁代码不生效
**解决方案：**
1. 确保在正确的页面执行代码
2. 检查控制台是否有错误信息
3. 尝试在无痕模式下测试
4. 禁用其他扩展程序

## 持久化解锁

为了让解锁效果持久化，可以：

1. **创建用户脚本（Tampermonkey）**
   - 安装 Tampermonkey 扩展
   - 创建新脚本，内容为 `advanced_unlock.js`
   - 设置在相关页面自动运行

2. **定期执行解锁代码**
   - 设置浏览器书签，内容为：
   ```javascript
   javascript:(function(){
       localStorage.setItem('vip','true');
       localStorage.setItem('premium','true');
       localStorage.setItem('unlimited','true');
       alert('解锁完成');
   })();
   ```

3. **使用浏览器扩展**
   - 创建自定义扩展来注入解锁代码
   - 确保在会译插件加载前执行

## 注意事项

1. **备份重要性**：始终在修改前备份原始文件
2. **更新问题**：插件更新可能会覆盖修改，需要重新应用解锁
3. **法律风险**：请确保您的使用符合相关法律法规
4. **功能稳定性**：修改后的插件可能会出现不稳定情况
5. **检测风险**：某些解锁方法可能被服务器检测到

## 恢复原始状态

如果需要恢复到原始状态：

1. **恢复文件**
   ```bash
   copy backup\*.bak .
   ```

2. **清除存储**
   ```javascript
   localStorage.clear();
   sessionStorage.clear();
   ```

3. **重置插件**
   - 在扩展管理页面禁用并重新启用插件
   - 或者卸载重装插件

## 🤖 支持的模型

解锁后可以访问的高级模型包括：
- **GPT-4 系列**：GPT-4, GPT-4 Turbo, GPT-4o
- **Claude 3 系列**：Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku
- **DeepSeek 系列**：DeepSeek V2, DeepSeek Coder
- **Gemini 系列**：Gemini Pro, Gemini Ultra
- **其他高级模型**：所有标记为"顶级模型"的AI

## 🔄 解锁效果验证

### 检查解锁状态
在开发者工具 Console 中执行：
```javascript
console.log('VIP状态:', localStorage.getItem('vip'));
console.log('Premium状态:', localStorage.getItem('premium'));
console.log('用户等级:', localStorage.getItem('user_level'));
console.log('订阅状态:', localStorage.getItem('subscription_status'));
```

### 功能测试
1. ✅ 尝试使用之前被限制的高级模型
2. ✅ 检查是否还有使用次数限制
3. ✅ 查看升级提示是否消失
4. ✅ 测试Token限制是否解除

## 📝 更新日志

- **v3.0**: 超级解锁版本
  - 新增反检测机制
  - 智能存储劫持
  - 深度函数Hook
  - 网络请求完全控制
  - 一键书签解锁
  - 持续监控维护

- **v2.0**: 终极解锁版本
  - 深度DOM操作
  - 内存状态劫持
  - 强化UI控制

- **v1.3**: 优化兼容性和稳定性
- **v1.2**: 增强持久化机制
- **v1.1**: 添加高级拦截和代理功能
- **v1.0**: 初始版本，基础解锁功能

---

**免责声明**：本指南仅供学习和研究目的。使用时请遵守相关法律法规和服务条款。作者不承担任何因使用本指南导致的后果。